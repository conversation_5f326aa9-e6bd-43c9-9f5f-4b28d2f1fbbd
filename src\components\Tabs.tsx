import React from "react";
import View from "./view";
import Button from "./button";
import { useSearchParams } from "react-router-dom";

interface Tab {
  value: string;
  label: string;
  content: React.ReactNode;
}

interface TabViewProps {
  tabs: Tab[];
  defaultValue?: string;
  className?: string;
}

const TabView: React.FC<TabViewProps> = ({ tabs, className }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  return (
    <View className={`w-full ${className}`}>
      <View className="flex gap-1 border-b border-slate-200 dark:border-slate-700">
        {tabs.map((tab) => (
          <Button
            key={tab.value}
            disabled={searchParams.get("tab") === tab.value}
            onClick={() => {
              if (searchParams.get("tab") === tab.value) return;
              const searchData = {
                ...searchParams,
                tab: tab.value,
                currentPage: tab.value === "system-settings" ? undefined : "1",
              };
              setSearchParams(JSON.parse(JSON.stringify(searchData)));
            }}
            variant="ghost"
            className={`px-4 py-2 text-sm font-medium transition-all duration-200 rounded-t-lg rounded-b-none relative
              ${
                searchParams.get("tab") === tab.value
                  ? "text-primary-600 dark:text-primary-400 bg-white dark:bg-slate-800 border-b-2 border-primary-600 dark:border-primary-400"
                  : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700/50"
              }`}
          >
            {tab.label}
          </Button>
        ))}
      </View>
      <View className="mt-6">
        {tabs
          .filter((filterdata) => {
            if (searchParams.get("tab") === filterdata.value) {
              return filterdata;
            }
          })
          .map((tab) => (
            <View key={tab.value}>{tab.content}</View>
          ))}
      </View>
    </View>
  );
};

export default TabView;
