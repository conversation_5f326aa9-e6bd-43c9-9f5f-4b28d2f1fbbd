import { useTest } from "@/actions/calls/test";
import BouncingLoader from "@/components/BouncingLoader";
import Button from "@/components/button";
import ActionMenu from "@/components/editDeleteAction";
import Modal from "@/components/Modal";
import PaginationComponent from "@/components/Pagination";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import { handleSortChange } from "@/utils/helperFunctions";
import {
  TEST_DETAILS_URL,
  TEST_EDIT_URL,
  TEST_FORM_URL,
  TEST_TABLE_URL,
} from "@/utils/urls/frontend";
import { Plus, FlaskConical, Activity, Users, CheckCircle } from "lucide-react";
import InfoCard from "@/components/ui/infoCard";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useNavigate, useSearchParams } from "react-router-dom";

const TestPage: React.FC<{}> = () => {
  const navigate = useNavigate();
  const { testListHandler, testDeleteHandler, cleanUp } = useTest();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const testListData = useSelector((state: any) => state.test.testListData);
  const [description, setDescription] = useState<null | string>(null);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      testListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };
  const modalDescriptionCloseHandler = () => {
    setDescription(null);
  };
  const handleDescription = (roleDescription: string) => {
    setDescription(roleDescription);
  };

  const sortOptions: SortOption[] = [
    { label: "Test Name (A-Z)", value: "test_name", order: "asc" },
    { label: "Test Name (Z-A)", value: "test_name", order: "desc" },
  ];
  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <Modal
        title="Test Name"
        isOpen={description !== null}
        onClose={modalDescriptionCloseHandler}
        description={description || ""}
      >
        <View className="flex justify-end gap-2">
          <Button
            className="text-black dark:text-white "
            variant="outline"
            onPress={modalDescriptionCloseHandler}
          >
            Cancel
          </Button>
        </View>
      </Modal>
      <Modal
        title="Test Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this data? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button variant="outline" onPress={modalCloseHandler}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onPress={() => {
              if (deleteId) {
                testDeleteHandler(deleteId, (success: boolean) => {
                  if (success) {
                    testListHandler(
                      searchParams?.get("currentPage") ?? 1,
                      () => {
                        modalCloseHandler();
                      }
                    );
                  }
                });
              }
            }}
          >
            Delete
          </Button>
        </View>
      </Modal>
      {/* Header Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex items-center gap-3">
          <View className="p-2 rounded-lg bg-primary/10">
            <FlaskConical className="h-6 w-6 text-primary" />
          </View>
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1"
            >
              Tests
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
              Manage hospital laboratory tests and diagnostics
            </Text>
          </View>
        </View>
      </View>

      {/* Stats Cards */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <InfoCard
          label="Total Tests"
          value={testListData?.total || 0}
          valueStyle="!text-blue-600 dark:!text-blue-400"
          icon={<FlaskConical size={20} />}
          iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Today's Tests"
          value={testListData?.data?.filter((test: any) => {
            const today = new Date().toISOString().split('T')[0];
            return test.created_at?.split('T')[0] === today;
          }).length || 0}
          valueStyle="!text-emerald-600 dark:!text-emerald-400"
          icon={<Activity size={20} />}
          iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Completed Tests"
          value={testListData?.data?.filter((test: any) => test.status === 'completed').length || 0}
          valueStyle="!text-green-600 dark:!text-green-400"
          icon={<CheckCircle size={20} />}
          iconStyle="!bg-gradient-to-br !from-green-100 !via-green-200 !to-green-300 dark:!from-green-800/40 dark:!via-green-700/40 dark:!to-green-600/40 !text-green-600 dark:!text-green-400 !shadow-lg !shadow-green-500/25 dark:!shadow-green-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Patients Tested"
          value={new Set(testListData?.data?.map((test: any) => test.patient_id)).size || 0}
          valueStyle="!text-purple-600 dark:!text-purple-400"
          icon={<Users size={20} />}
          iconStyle="!bg-gradient-to-br !from-purple-100 !via-purple-200 !to-purple-300 dark:!from-purple-800/40 dark:!via-purple-700/40 dark:!to-purple-600/40 !text-purple-600 dark:!text-purple-400 !shadow-lg !shadow-purple-500/25 dark:!shadow-purple-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />
      </View>

      <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
        {/* <View className="p-4 border-b border-neutral-200 bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center  dark:border-none">
          <View className="flex gap-2 w-full  justify-between items-center ">
            <SearchBar
              onSearch={(value: string) => {
                setSearchParams(
                  {
                    ...Object.fromEntries([...searchParams]),
                    currentPage: "1",
                    search: value,
                  },
                  { replace: true }
                );
              }}
              className="shadow-sm dark:shadow-none"
            />
            <View className="flex gap-3">
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(TEST_TABLE_URL + TEST_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Test
              </Button>
            </View>
          </View>
        </View> */}
        {/* Table */}
        <DynamicTable
          tableHeaders={[
            "Test Number",
            "Test Name",
            "Test Amount (Rs)",
            "Tax Amount (Rs)",
            // "Test Description",
            "Actions",
          ]}
          tableData={testListData?.data?.map((data: any) => [
            <Link to={TEST_TABLE_URL + TEST_DETAILS_URL + "/" + data.id}>
              {data?.test_number}
            </Link>,
            <Button
              variant="ghost"
              onClick={() => handleDescription(data?.test_name)}
              className="text-left hover:text-primary underline"
            >
              {data?.test_name?.length > 60 ? data?.test_name?.slice(0, 60) + "..."  : data?.test_name}
            </Button>,
            // data?.test_name,
            data?.test_price || "N/A",
            data?.tax_price || "N/A",
            // data?.test_description,
            <ActionMenu
              onEdit={() =>
                navigate(TEST_TABLE_URL + TEST_EDIT_URL + "/" + data.id)
              }
              onDelete={() => setDeleteId(data.id)}
            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                      search: value,
                    },
                    { replace: true }
                  );
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            action: (
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(TEST_TABLE_URL + TEST_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Test
              </Button>
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={testListData?.current_page}
                last_page={testListData?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
        {/* <PaginationComponent
          getPageNumberHandler={(page) => {
            setSearchParams(
              {
                ...Object.fromEntries([...searchParams]),
                currentPage: `${page}`,
              },
              { replace: true }
            );
          }}
          last_page={testListData?.last_page}
          current_page={testListData?.current_page}
        /> */}
      </Card>
    </React.Fragment>
  );
};

export default TestPage;
