import React from "react";
import Button from "./button";
import View from "./view";

interface PaginationProps {
  last_page?: number;
  current_page?: number;
  getPageNumberHandler?: (pageNuber: number) => void;
}

const PaginationComponent: React.FC<PaginationProps> = ({
  last_page = 1,
  current_page = 1,
  getPageNumberHandler,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= last_page && getPageNumberHandler) {
      getPageNumberHandler(page);
    }
  };

  return (
    <View>
      {/* <ul>
        {items.map((item, idx) => (
          <li key={idx}>{item.name}</li> // adjust based on your model
        ))}
      </ul> */}

      <View className="flex items-center gap-1 justify-end py-2">
        <Button
          variant="outline"
          size="small"
          disabled={current_page === 1}
          onPress={() => handlePageChange(current_page - 1)}
          className="px-3 py-1.5 text-xs"
        >
          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Previous
        </Button>

        {[...Array(last_page)].map((_, index) => {
          const pageNum = index + 1;
          return (
            <Button
              variant={pageNum === current_page ? "primary" : "ghost"}
              size="small"
              key={pageNum}
              onPress={() => handlePageChange(pageNum)}
              className={`
                px-2.5 py-1.5 text-xs min-w-[32px] h-8
                ${pageNum === current_page
                  ? "bg-primary-600 text-white shadow-sm"
                  : "text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
                }
              `}
            >
              {pageNum}
            </Button>
          );
        })}

        <Button
          variant="outline"
          size="small"
          disabled={current_page === last_page}
          onPress={() => handlePageChange(current_page + 1)}
          className="px-3 py-1.5 text-xs"
        >
          Next
          <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Button>
      </View>
    </View>
  );
};

export default PaginationComponent;
