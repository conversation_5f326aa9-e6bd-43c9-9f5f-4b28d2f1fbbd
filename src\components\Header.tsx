import React from "react";
import { PanelRightClose } from "lucide-react";
import Button from "@/components/button";
import { Link } from "react-router-dom";
import { USER_PROFILE_URL } from "@/utils/urls/backend";
import { useSelector } from "react-redux";
import View from "./view";
import ImageComponent from "./ui/ImageComponent";
import Text from "./text";
// import { useSelector } from "react-redux";

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean
}




const Header: React.FC<HeaderProps> = ({ toggleSidebar,sidebarOpen }) => {
  const settingsData = useSelector((state: any) => state.systemSettings.settings);
  
  return (
    <header className="h-16 border-b border-border dark:border-none bg-card dark:bg-background text-card-foreground shadow-sm dark:shadow-none transition-colors duration-200">
      <View className="flex h-full items-center justify-between px-4">
        {
          !sidebarOpen && (
            <Button
          onClick={toggleSidebar}
          className="text-muted-foreground hover:text-black dark:hover:text-white"
          variant="ghost"
        //   size="icon"
        >
          <PanelRightClose size={20} />
          {/* <Menu size={20} /> */}
        </Button>
          )
        }

        {/* Search */}
        {/* <div className="hidden md:block relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-text-lighter" />
          </div>
          <input
            type="search"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 w-full bg-neutral-100 border-none rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none"
          />
        </div> */}

        <View className="hidden md:block w-64">
          {/* <SearchBar /> */}
        </View>

        {/* User Menu & Notifications */}
        <View className="flex items-center gap-3">
          {/* Theme Toggle */}
          {/* <ThemeToggle /> */}

          {/* <Button
            className="relative p-1.5 text-neutral-500 hover:bg-neutral-100 rounded-lg"
            variant="ghost"
            // size="icon"
          >
            <Bell size={20} />
            <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-primary-500"></span>
          </Button> */}
          <Link to={USER_PROFILE_URL} className="text-sm font-medium text-primary-600">
          <Button
            className="flex items-center gap-2"
            variant="ghost"
          >
            {
              settingsData?.profile_image ? (
                <View className="h-8 w-8 rounded-full overflow-hidden">
                  <ImageComponent
          src={import.meta.env.VITE_APP_URL + settingsData?.profile_image}
          alt={"User profile image"}
          className="rounded-full object-cover h-full"
        />
                </View>
                
              ) : (
                <View className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                  <Text as="span" className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center ">
                   U
                  </Text>
                </View>
              )

            }
            {/* <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
              JD
            </div> */}
          </Button>
          </Link>
        </View>
      </View>
    </header>
  );
};

export default Header;