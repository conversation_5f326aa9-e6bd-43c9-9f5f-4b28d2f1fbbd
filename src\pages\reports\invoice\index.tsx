import Filter from "@/pages/filter";
import Text from "@/components/text";
import View from "@/components/view";
import Input from "@/components/input";
import Button from "@/components/button";
import { RootState } from "@/actions/store";
import { Card } from "@/components/ui/card";
import DataSort from "@/components/SortData";
import InfoCard from "@/components/ui/infoCard";
import { Banknote, FileText, Percent, Smartphone, TrendingUp, UserX, Users, CreditCard } from "lucide-react";
import Chart from 'react-apexcharts';
import { useSearchParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import SearchBar from "@/components/ui/search-bar";
import { useDispatch, useSelector } from "react-redux";
import DynamicTable from "@/components/ui/DynamicTable";
import BouncingLoader from "@/components/BouncingLoader";
import PaginationComponent from "@/components/Pagination";
import { handleSortChange } from "@/utils/helperFunctions";
import DateRangePicker from "@/components/DateRangePicker";
import { clearList } from "@/actions/slices/invoiceReport";
import { INVOICE_REPORT_DOWNLOAD_URL } from "@/utils/urls/backend";
import { useInvoiceReport } from "@/actions/calls/reports/invoice";
import SingleSelector from "@/components/SingleSelector";
import { useAmountType } from "@/actions/calls/amountType";


const Invoice: React.FC<{}> = ({}) => {
  //   const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loadingStatus, setIsLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const { amountTypeDropdownHandler } = useAmountType();
  const invoiceReportList = useSelector(
    (state: RootState) => state.invoiceReport.invoiceReportList
  );

  const currencySymbol = useSelector(
    (state: RootState) => state.systemSettings.settings.currency_symbol
  );
  
  
  
  

  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const { cleanUp, getListApi } = useInvoiceReport();

  const sortOptions: any[] = [
    { label: "Patient Name (A-Z)", value: "patient_name", order: "asc" },
    { label: "Patient Name (Z-A)", value: "patient_name", order: "desc" },
    { label: "Patient Email (A-Z)", value: "patient_email", order: "asc" },
    { label: "Patient Email (Z-A)", value: "patient_email", order: "desc" },
    { label: "Patient Phone (A-Z)", value: "patient_phone", order: "asc" },
    { label: "Patient Phone (Z-A)", value: "patient_phone", order: "desc" },
    { label: "Patient Number (A-Z)", value: "patient_number", order: "asc" },
    { label: "Patient Number (Z-A)", value: "patient_number", order: "desc" },
    { label: "Doctor Name (A-Z)", value: "doctor_name", order: "asc" },
    { label: "Doctor Name (Z-A)", value: "doctor_name", order: "desc" },
    { label: "Doctor Email (A-Z)", value: "doctor_email", order: "asc" },
    { label: "Doctor Email (Z-A)", value: "doctor_email", order: "desc" },
    { label: "Doctor Phone (A-Z)", value: "doctor_phone", order: "asc" },
    { label: "Doctor Phone (Z-A)", value: "doctor_phone", order: "desc" },
    {
      label: "Referred By Name (A-Z)",
      value: "referred_by_name",
      order: "asc",
    },
    {
      label: "Referred By Name (Z-A)",
      value: "referred_by_name",
      order: "desc",
    },
    {
      label: "Collected Amount (A-Z)",
      value: "collected_amount",
      order: "asc",
    },
    {
      label: "Collected Amount (Z-A)",
      value: "collected_amount",
      order: "desc",
    },
    { label: "Balance Amount (A-Z)", value: "balanced_amount", order: "asc" },
    { label: "Balance Amount (Z-A)", value: "balanced_amount", order: "desc" },
  ];

  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDropdownData
  );

  useEffect(() => {
    amountTypeDropdownHandler(() => {});
  }, []);
  

  const [activeSort, setActiveSort] = useState<any | null>(sortOptions[0]);

  useEffect(() => {
    getListApi(
      searchParams.get("page") ?? 1,
      () => {},
      (loadingStatus) => {
        setIsLoading(
          loadingStatus == "pending"
            ? true
            : loadingStatus == "failed"
            ? true
            : loadingStatus == "success" && false
        );
      },
      searchParams.get("search") ?? null,
      searchParams.get("sort_by") ?? null,
      searchParams.get("sort_order") ?? null,
      searchParams?.get("from_date") ?? null,
      searchParams?.get("to_date") ?? null,
      filterData
    );
    return () => {
      cleanUp();
      dispatch(clearList());
    };
  }, [
    filterData,
    searchParams.get("page"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams?.get("to_date"),
    searchParams?.get("from_date"),
    searchParams.get("sort_order"),
  ]);

    // console.log(invoiceReportList, "invoiceReportList");
    const cashAmount = invoiceReportList?.typesOfPayment.length > 0 ? invoiceReportList?.typesOfPayment?.filter((item: any) => (
     item.payment_type === "Cash"
  ))[0].total_collected : 0;

  const upiOrOnlineAmount = invoiceReportList?.typesOfPayment.length > 0 ? invoiceReportList?.typesOfPayment?.filter((item: any) => (
     item.payment_type !== "Cash"
  )).reduce((acc: any, item: any) => acc + item.total_collected, 0) : 0;

  const discountAmount = invoiceReportList?.paymentBreakPoint.length > 0 ? invoiceReportList?.paymentBreakPoint?.reduce((acc: any, item: any) => acc + Number(item.total_discount), 0) : 0;

  const downloadExpensesExcel = async () => {
    try {
      setIsLoading(true);
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token");

      const response = await fetch(
        `${baseUrl}${INVOICE_REPORT_DOWNLOAD_URL}?page=${
          searchParams.get("page") ?? 1
        }${
          searchParams.get("search")
            ? "&search=" + searchParams.get("search")
            : ""
        }${
          searchParams.get("sort_by")
            ? "&sort_by=" + searchParams.get("sort_by")
            : ""
        }${
          searchParams.get("sort_order")
            ? "&sort_order=" + searchParams.get("sort_order")
            : ""
        }${
          searchParams?.get("from_date")
            ? "&from_date=" + searchParams?.get("from_date")
            : ""
        }${
          searchParams?.get("to_date")
            ? "&to_date=" + searchParams?.get("to_date")
            : ""
        }`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        }
      );

      if (!response.ok) {
        setIsLoading(false);
        throw new Error("Excel download failed");
      }
      setIsLoading(false);

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "expenses-report.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);
    } catch (error) {
      setIsLoading(false);
      alert("Failed to download Excel report");
    }
  };

  return (
    <React.Fragment>
      <View className="fixed top-4 left-0  w-full z-50">
        <BouncingLoader isLoading={loadingStatus} />
      </View>

      {/* Header Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex items-center gap-3 mb-4">
          <View className="p-2 rounded-lg bg-primary/10">
            <Banknote className="h-6 w-6 text-primary" />
          </View>
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1"
            >
              Invoice Report
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
              Track billing, payments, and revenue analytics
            </Text>
          </View>
        </View>

        <View className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <View className="flex items-center gap-4">
            {invoiceReportList?.table?.data?.length > 0 && (
              <Button
                variant="outline"
                onPress={downloadExpensesExcel}
                className="flex items-center gap-2"
              >
                <FileText size={16} />
                Download Invoice Report
              </Button>
            )}
          </View>
          <View>
            <Text
              as="label"
              className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
            >
              Select Date Range
            </Text>
            <DateRangePicker
              placeholder="Choose your dates"
            />
          </View>
        </View>
      </View>

      {/* Stats Layout: 4 Cards (2x2) + Chart */}
      <View className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Left Side - 4 Cards in 2x2 Grid */}
        <View className="lg:col-span-2">
          <View className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InfoCard
              label="Amount Billed"
              value={`${currencySymbol}${invoiceReportList?.collected_amount || '0'}`}
              valueStyle="!text-blue-600 dark:!text-blue-400"
              icon={<FileText size={20} />}
              iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
              className="hover:scale-[1.02] transition-transform duration-200"
            />

            <InfoCard
              label="Amount Collected"
              value={`${currencySymbol}${invoiceReportList?.includeInvoiceAmount || '0'}`}
              valueStyle="!text-emerald-600 dark:!text-emerald-400"
              icon={<TrendingUp size={20} />}
              iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
              className="hover:scale-[1.02] transition-transform duration-200"
            />

            <InfoCard
              label="Cancelled by Patient"
              value={`${currencySymbol}${invoiceReportList?.excludeInvoiceAmount || '0'}`}
              valueStyle="!text-red-600 dark:!text-red-400"
              icon={<UserX size={20} />}
              iconStyle="!bg-gradient-to-br !from-red-100 !via-red-200 !to-red-300 dark:!from-red-800/40 dark:!via-red-700/40 dark:!to-red-600/40 !text-red-600 dark:!text-red-400 !shadow-lg !shadow-red-500/25 dark:!shadow-red-400/20"
              className="hover:scale-[1.02] transition-transform duration-200"
            />

            <InfoCard
              label="Discount Amount"
              value={`${currencySymbol}${discountAmount || '0'}`}
              valueStyle="!text-orange-600 dark:!text-orange-400"
              icon={<Percent size={20} />}
              iconStyle="!bg-gradient-to-br !from-orange-100 !via-orange-200 !to-orange-300 dark:!from-orange-800/40 dark:!via-orange-700/40 dark:!to-orange-600/40 !text-orange-600 dark:!text-orange-400 !shadow-lg !shadow-orange-500/25 dark:!shadow-orange-400/20"
              className="hover:scale-[1.02] transition-transform duration-200"
            />
          </View>
        </View>

        {/* Right Side - Payment Collection Chart */}
        <View className="lg:col-span-1">
          <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 h-full relative overflow-hidden">
            {/* Glow effect background */}
            <View className="absolute inset-0 bg-gradient-to-br from-emerald-50/30 via-purple-50/30 to-blue-50/30 dark:from-emerald-900/10 dark:via-purple-900/10 dark:to-blue-900/10 rounded-xl"></View>
            <View className="relative z-10">
            <View className="flex items-center gap-3 mb-6">
              <View className="p-2 rounded-lg bg-gradient-to-br from-purple-100 to-emerald-100 dark:from-purple-900/40 dark:to-emerald-900/40">
                <CreditCard className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </View>
              <View>
                <Text className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  Payment Collection
                </Text>
                <Text className="text-sm text-slate-600 dark:text-slate-400">
                  Cash vs Digital breakdown
                </Text>
              </View>
            </View>

            <Chart
              options={{
                chart: {
                  type: 'radialBar',
                  toolbar: { show: false },
                  background: 'transparent',
                  dropShadow: {
                    enabled: true,
                    top: 0,
                    left: 0,
                    blur: 15,
                    opacity: 0.2,
                    color: '#000',
                  },
                },
                plotOptions: {
                  radialBar: {
                    startAngle: -135,
                    endAngle: 135,
                    hollow: {
                      margin: 0,
                      size: '45%',
                      background: 'transparent',
                    },
                    track: {
                      background: '#f1f5f9',
                      strokeWidth: '100%',
                      margin: 12,
                      dropShadow: {
                        enabled: true,
                        top: 0,
                        left: 0,
                        blur: 4,
                        opacity: 0.1,
                      },
                    },
                    dataLabels: {
                      name: {
                        fontSize: '13px',
                        fontWeight: '600',
                        color: '#64748b',
                        offsetY: -15,
                      },
                      value: {
                        fontSize: '16px',
                        fontWeight: 'bold',
                        offsetY: 0,
                        formatter: function () {
                          return '';
                        },
                      },
                      total: {
                        show: true,
                        label: 'Total Collection',
                        fontSize: '12px',
                        fontWeight: '600',
                        color: '#64748b',
                        formatter: function () {
                          const total = (parseFloat(cashAmount || '0') + parseFloat(upiOrOnlineAmount || '0'));
                          return `${currencySymbol}${total.toLocaleString()}`;
                        },
                      },
                    },
                  },
                },
                fill: {
                  type: 'gradient',
                  gradient: {
                    shade: 'light',
                    type: 'vertical',
                    shadeIntensity: 0.8,
                    gradientToColors: ['#34d399', '#a78bfa', '#f87171', '#fbbf24'],
                    inverseColors: false,
                    opacityFrom: 0.9,
                    opacityTo: 0.6,
                    stops: [0, 50, 100],
                    colorStops: [
                      {
                        offset: 0,
                        color: '#10b981',
                        opacity: 0.9,
                      },
                      {
                        offset: 50,
                        color: '#34d399',
                        opacity: 0.8,
                      },
                      {
                        offset: 100,
                        color: '#6ee7b7',
                        opacity: 0.7,
                      },
                    ],
                  },
                },
                stroke: {
                  lineCap: 'round',
                  dashArray: 0,
                  width: 0,
                },
                labels: ['Cash Collection', 'UPI/Online', 'Cancelled', 'Discount'],
                colors: ['#10b981', '#8b5cf6', '#ef4444', '#f59e0b'],
                legend: {
                  show: true,
                  position: 'bottom',
                  horizontalAlign: 'center',
                  fontSize: '11px',
                  fontWeight: '500',
                  labels: {
                    colors: ['#64748b'],
                  },
                  markers: {
                    size: 8,
                    strokeWidth: 2,
                    fillColors: ['#10b981', '#8b5cf6', '#ef4444', '#f59e0b'],
                  },
                },
                tooltip: {
                  enabled: true,
                  style: {
                    fontSize: '12px',
                  },
                  y: {
                    formatter: function (_val: number, opts: any) {
                      const seriesIndex = opts.seriesIndex;
                      if (seriesIndex === 0) {
                        return `Cash: ${currencySymbol}${cashAmount || '0'}`;
                      } else if (seriesIndex === 1) {
                        return `UPI/Online: ${currencySymbol}${upiOrOnlineAmount || '0'}`;
                      } else if (seriesIndex === 2) {
                        return `Cancelled: ${currencySymbol}${invoiceReportList?.excludeInvoiceAmount || '0'}`;
                      } else {
                        return `Discount: ${currencySymbol}${discountAmount || '0'}`;
                      }
                    },
                  },
                },
                responsive: [
                  {
                    breakpoint: 1024,
                    options: {
                      chart: {
                        height: 300,
                      },
                      plotOptions: {
                        radialBar: {
                          hollow: {
                            size: '50%',
                          },
                        },
                      },
                    },
                  },
                ],
              }}
              series={[
                Math.min(
                  ((parseFloat(cashAmount || '0') /
                    Math.max(parseFloat(cashAmount || '0') + parseFloat(upiOrOnlineAmount || '0'), 1)) * 100) || 0,
                  100
                ),
                Math.min(
                  ((parseFloat(upiOrOnlineAmount || '0') /
                    Math.max(parseFloat(cashAmount || '0') + parseFloat(upiOrOnlineAmount || '0'), 1)) * 100) || 0,
                  100
                ),
                Math.min(
                  ((parseFloat(invoiceReportList?.excludeInvoiceAmount || '0') /
                    Math.max(parseFloat(invoiceReportList?.collected_amount || '0'), 1)) * 100) || 0,
                  100
                ),
                Math.min(
                  ((parseFloat(discountAmount || '0') /
                    Math.max(parseFloat(invoiceReportList?.collected_amount || '0'), 1)) * 100) || 0,
                  100
                )
              ]}
              type="radialBar"
              height={380}
            />
          </View>
        </View>
      </View>



      <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
        {/* Table */}
        <DynamicTable
          // isLoading={loadingStatus}
          tableHeaders={[
            "Patient Name",
            "Patient Email",
            "Patient Phone",
            "Patient Number",
            "Doctor Name",
            "Doctor Email",
            "Doctor Phone",
            "Referred By Name",
            "Collected Amount",
            // "Balance Amount",
            "Mode of Payment",
          ]}
          tableData={invoiceReportList?.table?.data?.map((invoice: any) => [
            invoice.patient_name,
            invoice.patient_email,
            invoice.patient_phone,
            invoice.patient_number,
            invoice.doctor_name,
            invoice.doctor_email,
            invoice.doctor_phone,
            invoice.referred_by_name,
            `Rs ${invoice.collected_amount}`,
            // `Rs ${invoice.balanced_amount}`,
            invoice.payment_type,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(val) =>
                  setSearchParams({
                    ...Object.fromEntries(searchParams),
                    search: val,
                    currentPage: "1",
                  })
                }
              />
            ),
            filter: (
              <Filter
                onResetFilter={() => {
                  setFilterData(null);
                }}
                title="Expense Filter"
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_name" placeholder="Patient name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="doctor_name" placeholder="Doctor name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="referred_by_name"
                      placeholder="Referred by name"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="collected_amount"
                      placeholder="Collected amount"
                      type="number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="balanced_amount"
                      placeholder="Balanced amount"
                      type="number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <SingleSelector
                      required={true}
                      id="payment_type"
                      name="payment_type"
                      label="Mode of Payment"
                      options={amountTypeData?.map((item: any) => ({
                        value: item.amount_for,
                        label: item.amount_for,
                      }))}
                      placeholder="Select Mode of Payment"
                    />
                  </View>,
                ]}
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={invoiceReportList?.table?.current_page}
                last_page={invoiceReportList?.table?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};

export default Invoice;
