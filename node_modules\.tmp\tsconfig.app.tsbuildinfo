{"root": ["../../src/app.tsx", "../../src/withlogin.tsx", "../../src/withoutlogin.tsx", "../../src/checkinternetconnection.tsx", "../../src/dummydatafile.ts", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/actions/api.ts", "../../src/actions/store.ts", "../../src/actions/calls/allergies.ts", "../../src/actions/calls/amounttype.ts", "../../src/actions/calls/appointments.ts", "../../src/actions/calls/auth.ts", "../../src/actions/calls/chiefcomplaints.ts", "../../src/actions/calls/comorbidities.ts", "../../src/actions/calls/consultation.ts", "../../src/actions/calls/consultationfees.ts", "../../src/actions/calls/dashboard.ts", "../../src/actions/calls/department.ts", "../../src/actions/calls/diagnosis.ts", "../../src/actions/calls/diet.ts", "../../src/actions/calls/dre.ts", "../../src/actions/calls/examination.ts", "../../src/actions/calls/expenses.ts", "../../src/actions/calls/findings.ts", "../../src/actions/calls/fistula.ts", "../../src/actions/calls/foodadvice.ts", "../../src/actions/calls/geography.ts", "../../src/actions/calls/invoice.ts", "../../src/actions/calls/management.ts", "../../src/actions/calls/medicine.ts", "../../src/actions/calls/medicinecategory.ts", "../../src/actions/calls/medicinecategorymapping.ts", "../../src/actions/calls/onexamination.ts", "../../src/actions/calls/opd.ts", "../../src/actions/calls/patient.ts", "../../src/actions/calls/patienttest.ts", "../../src/actions/calls/postsurgeryfollowup.ts", "../../src/actions/calls/proctoscopy.ts", "../../src/actions/calls/roles.ts", "../../src/actions/calls/room.ts", "../../src/actions/calls/servicecost.ts", "../../src/actions/calls/surgicalhistory.ts", "../../src/actions/calls/systemsettings.ts", "../../src/actions/calls/test.ts", "../../src/actions/calls/uesimage.ts", "../../src/actions/calls/user.ts", "../../src/actions/calls/yogaasana.ts", "../../src/actions/calls/consultation/dynamicfieldsections.ts", "../../src/actions/calls/doshaanalysis/prakriti.ts", "../../src/actions/calls/reports/expenses.ts", "../../src/actions/calls/reports/invoice.ts", "../../src/actions/slices/allergies.ts", "../../src/actions/slices/amounttype.ts", "../../src/actions/slices/appointments.ts", "../../src/actions/slices/auth.ts", "../../src/actions/slices/chiefcomplaints.ts", "../../src/actions/slices/comorbidities.ts", "../../src/actions/slices/consultation.ts", "../../src/actions/slices/consultationfees.ts", "../../src/actions/slices/dashboard.ts", "../../src/actions/slices/departments.ts", "../../src/actions/slices/diagnosis.ts", "../../src/actions/slices/diet.ts", "../../src/actions/slices/doshaanalysis.ts", "../../src/actions/slices/dre.ts", "../../src/actions/slices/examination.ts", "../../src/actions/slices/expensereport.ts", "../../src/actions/slices/expenses.ts", "../../src/actions/slices/findings.ts", "../../src/actions/slices/fistula.ts", "../../src/actions/slices/foodadvice.ts", "../../src/actions/slices/geography.ts", "../../src/actions/slices/invoice.ts", "../../src/actions/slices/invoicereport.ts", "../../src/actions/slices/management.ts", "../../src/actions/slices/medicalstatus.ts", "../../src/actions/slices/medicine.ts", "../../src/actions/slices/medicinecategory.ts", "../../src/actions/slices/medicinecategorymapping.ts", "../../src/actions/slices/onexamination.ts", "../../src/actions/slices/opd.ts", "../../src/actions/slices/patient.ts", "../../src/actions/slices/patienttest.ts", "../../src/actions/slices/postsurgery.ts", "../../src/actions/slices/proctoscopy.ts", "../../src/actions/slices/roleslice.ts", "../../src/actions/slices/room.ts", "../../src/actions/slices/servicecost.ts", "../../src/actions/slices/surgicalhistory.ts", "../../src/actions/slices/systemsettingsslice.ts", "../../src/actions/slices/test.ts", "../../src/actions/slices/userslice.ts", "../../src/actions/slices/yogaasana.ts", "../../src/actions/slices/consultation/dynamicfieldsections.ts", "../../src/components/addcost.tsx", "../../src/components/bouncingloader.tsx", "../../src/components/capture.tsx", "../../src/components/captureexample.tsx", "../../src/components/checkbox.tsx", "../../src/components/collapsiblecontainer.tsx", "../../src/components/collapsiblelist.tsx", "../../src/components/dashbaordlaytout.tsx", "../../src/components/dashboardlayout.tsx", "../../src/components/datanotfount.tsx", "../../src/components/daterangepicker.tsx", "../../src/components/dropdown.tsx", "../../src/components/editabletable.tsx", "../../src/components/filter.tsx", "../../src/components/followupmodal.tsx", "../../src/components/header.tsx", "../../src/components/jellytrianglespinner.tsx", "../../src/components/medicinessection.tsx", "../../src/components/modal.tsx", "../../src/components/multiselect.tsx", "../../src/components/multiselectwithdropdown.tsx", "../../src/components/multiselector.tsx", "../../src/components/multiselector2.tsx", "../../src/components/pagination.tsx", "../../src/components/radiobuttons.tsx", "../../src/components/searchselect.tsx", "../../src/components/select.tsx", "../../src/components/sidebar.tsx", "../../src/components/singleselector.tsx", "../../src/components/sortdata.tsx", "../../src/components/tabbedcollapsible.tsx", "../../src/components/tabs.tsx", "../../src/components/textarea.tsx", "../../src/components/tiptaptexteditor.tsx", "../../src/components/transferlist.tsx", "../../src/components/upload.tsx", "../../src/components/button.tsx", "../../src/components/countrystatedropdown.tsx", "../../src/components/editdeleteaction.tsx", "../../src/components/input.tsx", "../../src/components/offline.tsx", "../../src/components/resetuserpassword.tsx", "../../src/components/text.tsx", "../../src/components/view.tsx", "../../src/components/sidebar/sidebardropdown.tsx", "../../src/components/sidebar/sidebardropdownitem.tsx", "../../src/components/sidebar/index.ts", "../../src/components/ui/dynamictable.tsx", "../../src/components/ui/imagecomponent.tsx", "../../src/components/ui/radiogroup.tsx", "../../src/components/ui/sidebaritem.tsx", "../../src/components/ui/textbg.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/infocard.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/search-bar.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/theme-toggle.tsx", "../../src/components/ui/toast.tsx", "../../src/components/ui/toaster.tsx", "../../src/components/ui/use-toast.ts", "../../src/context-api/auth.tsx", "../../src/context-api/authcontext.tsx", "../../src/contexts/colorcontext.tsx", "../../src/contexts/themecontext.tsx", "../../src/interfaces/index.ts", "../../src/interfaces/allergies/index.ts", "../../src/interfaces/api/index.ts", "../../src/interfaces/appointments/index.ts", "../../src/interfaces/components/button/index.ts", "../../src/interfaces/components/input/dropdownprops.ts", "../../src/interfaces/components/input/input.ts", "../../src/interfaces/components/input/multiselector.ts", "../../src/interfaces/components/input/radiobuttons.ts", "../../src/interfaces/components/input/selectprops.ts", "../../src/interfaces/components/input/uploadprops.ts", "../../src/interfaces/components/multiselect/index.ts", "../../src/interfaces/components/radiogropus/radiogroups.ts", "../../src/interfaces/components/separator/index.ts", "../../src/interfaces/components/sidebar/dropdown.ts", "../../src/interfaces/components/tabs/index.ts", "../../src/interfaces/components/text/index.ts", "../../src/interfaces/consultation/index.ts", "../../src/interfaces/consultation/postsurgeryfollowup/index.ts", "../../src/interfaces/context/auth.ts", "../../src/interfaces/context/room.ts", "../../src/interfaces/dashboard/index.ts", "../../src/interfaces/departments/index.ts", "../../src/interfaces/doshaanalysis/index.ts", "../../src/interfaces/dre/index.ts", "../../src/interfaces/examination/index.ts", "../../src/interfaces/fees/test.ts", "../../src/interfaces/findings/index.ts", "../../src/interfaces/fistula/index.ts", "../../src/interfaces/histories/family_history.ts", "../../src/interfaces/histories/personal_history.ts", "../../src/interfaces/image/index.ts", "../../src/interfaces/investigation/index.ts", "../../src/interfaces/invoice/index.ts", "../../src/interfaces/invoice/items.ts", "../../src/interfaces/ipdcases/index.ts", "../../src/interfaces/master/amount types/index.ts", "../../src/interfaces/master/beds/index.ts", "../../src/interfaces/master/chiefcomplaints/index.ts", "../../src/interfaces/master/consultatoin fees(cost)/index.ts", "../../src/interfaces/master/diet/index.ts", "../../src/interfaces/master/rooms/index.ts", "../../src/interfaces/master/servicecost/index.ts", "../../src/interfaces/medicines/index.ts", "../../src/interfaces/medicines/medicine_category/index.ts", "../../src/interfaces/opdcases/index.ts", "../../src/interfaces/patients/complaints.ts", "../../src/interfaces/patients/index.ts", "../../src/interfaces/patients/medical_info.ts", "../../src/interfaces/prescription/index.ts", "../../src/interfaces/proctoscopy/index.ts", "../../src/interfaces/roles/index.tsx", "../../src/interfaces/slices/allergies.ts", "../../src/interfaces/slices/appointments.ts", "../../src/interfaces/slices/auth.ts", "../../src/interfaces/slices/comorbidities.ts", "../../src/interfaces/slices/consultation.ts", "../../src/interfaces/slices/dashboard.ts", "../../src/interfaces/slices/departmenttype.ts", "../../src/interfaces/slices/diagnosis.ts", "../../src/interfaces/slices/expenses.ts", "../../src/interfaces/slices/foodadvice.ts", "../../src/interfaces/slices/geography.ts", "../../src/interfaces/slices/invoice.ts", "../../src/interfaces/slices/management.ts", "../../src/interfaces/slices/medicine.ts", "../../src/interfaces/slices/medicinecategory.ts", "../../src/interfaces/slices/medicinecategorymapping.ts", "../../src/interfaces/slices/onexamination.ts", "../../src/interfaces/slices/opd.ts", "../../src/interfaces/slices/patient.ts", "../../src/interfaces/slices/patienttest.ts", "../../src/interfaces/slices/roles.ts", "../../src/interfaces/slices/room.ts", "../../src/interfaces/slices/surgicalhistory.ts", "../../src/interfaces/slices/systemsettings.ts", "../../src/interfaces/slices/test.ts", "../../src/interfaces/slices/user.ts", "../../src/interfaces/slices/yogaasana.ts", "../../src/interfaces/systemsettings/index.ts", "../../src/interfaces/test/index.ts", "../../src/interfaces/users/index.ts", "../../src/pages/linkexpired.tsx", "../../src/pages/loading.tsx", "../../src/pages/notfound.tsx", "../../src/pages/findings/findingdetail.tsx", "../../src/pages/findings/findingspage.tsx", "../../src/pages/allergy/allergydetail.tsx", "../../src/pages/allergy/allergypage.tsx", "../../src/pages/amount types/amounttypedetailspage.tsx", "../../src/pages/amount types/amounttypepage.tsx", "../../src/pages/appointments/appointmentdetailspage.tsx", "../../src/pages/appointments/appointmentpage.tsx", "../../src/pages/appointments/index.tsx", "../../src/pages/chiefcomplaints/chiefcomplaintdetail.tsx", "../../src/pages/chiefcomplaints/chiefcomplaintpage.tsx", "../../src/pages/comorbidities/comorbiditiesdetail.tsx", "../../src/pages/comorbidities/comorbiditiespage.tsx", "../../src/pages/consultation/consultationdetail.tsx", "../../src/pages/consultation/consultationpage.tsx", "../../src/pages/consultationfees/consultatoinfeesdetail.tsx", "../../src/pages/consultationfees/consultatoinfeespage.tsx", "../../src/pages/dashboard/home.tsx", "../../src/pages/departments/departments/departmentdetails.tsx", "../../src/pages/departments/departments/departmentspage.tsx", "../../src/pages/diagnosis/diagnosisdetail.tsx", "../../src/pages/diagnosis/diagnosispage.tsx", "../../src/pages/diet/dietdetailspage.tsx", "../../src/pages/diet/dietpage.tsx", "../../src/pages/doshaanalysis/doshaanalysispage.tsx", "../../src/pages/doshaanalysis/index.ts", "../../src/pages/dre/dredetailspage.tsx", "../../src/pages/dre/drepage.tsx", "../../src/pages/examinations/examinationdetailspage.tsx", "../../src/pages/examinations/examinationspage.tsx", "../../src/pages/expenses/expensedetail.tsx", "../../src/pages/expenses/expensepage.tsx", "../../src/pages/filter/index.tsx", "../../src/pages/fistula/fistuladetailspage.tsx", "../../src/pages/fistula/fistulapage.tsx", "../../src/pages/foodadvice/foodadvicepage.tsx", "../../src/pages/forms/patient tests/sectionone.tsx", "../../src/pages/forms/patient tests/sectionthree.tsx", "../../src/pages/forms/patient tests/sectiontwo.tsx", "../../src/pages/forms/patient tests/patienttestform.tsx", "../../src/pages/forms/patient tests/patienttestformoptions.ts", "../../src/pages/forms/patient tests/validationform.ts", "../../src/pages/forms/allergy/sectionone.tsx", "../../src/pages/forms/allergy/allergy.tsx", "../../src/pages/forms/allergy/allergyformoptions.ts", "../../src/pages/forms/allergy/validationform.tsx", "../../src/pages/forms/amount type/amounttype.tsx", "../../src/pages/forms/amount type/sectionone.tsx", "../../src/pages/forms/amount type/amounttypeformoptions.ts", "../../src/pages/forms/amount type/validationform.tsx", "../../src/pages/forms/appointmentsform/sectionone.tsx", "../../src/pages/forms/appointmentsform/sectiontwo.tsx", "../../src/pages/forms/appointmentsform/appointment.tsx", "../../src/pages/forms/appointmentsform/appointmentformoptions.ts", "../../src/pages/forms/appointmentsform/validationform.ts", "../../src/pages/forms/authentication/forgotpassword.tsx", "../../src/pages/forms/authentication/resetpassword.tsx", "../../src/pages/forms/authentication/login.tsx", "../../src/pages/forms/chief complaints form/chiefcomplaintsform.tsx", "../../src/pages/forms/chief complaints form/chiefcomplaintsformoptions.ts", "../../src/pages/forms/chief complaints form/validationform.ts", "../../src/pages/forms/comorbidities/comorbidities.tsx", "../../src/pages/forms/comorbidities/sectionone.tsx", "../../src/pages/forms/comorbidities/validationform.ts", "../../src/pages/forms/consultation fees form/consultationfeesform.tsx", "../../src/pages/forms/consultation fees form/consultationformoptions.ts", "../../src/pages/forms/consultation fees form/validationform.ts", "../../src/pages/forms/consultationform/nonproctologyexaminationsection.tsx", "../../src/pages/forms/consultationform/proctologyexaminationsection.tsx", "../../src/pages/forms/consultationform/sectionfour.tsx", "../../src/pages/forms/consultationform/sectionone.tsx", "../../src/pages/forms/consultationform/sectionthree.tsx", "../../src/pages/forms/consultationform/sectiontwo.tsx", "../../src/pages/forms/consultationform/consultation.tsx", "../../src/pages/forms/consultationform/consultation1.tsx", "../../src/pages/forms/consultationform/consultationformoptions.ts", "../../src/pages/forms/consultationform/consultationmodels.tsx", "../../src/pages/forms/consultationform/procnonproccommonfields.tsx", "../../src/pages/forms/consultationform/validationform copy.ts", "../../src/pages/forms/consultationform/validationform.ts", "../../src/pages/forms/consultationform/non-proptologysections/non-proctologyexaminationsection.tsx", "../../src/pages/forms/consultationform/post sergery follow-up form/postsurgeryfollowupform.tsx", "../../src/pages/forms/consultationform/post sergery follow-up form/postsurgeryfollowupformoptions.ts", "../../src/pages/forms/consultationform/post sergery follow-up form/validationform.tsx", "../../src/pages/forms/consultationform/proptologysections/proctologyexaminationsection.tsx", "../../src/pages/forms/consultationform/proptologysections/postexaminationsection.tsx", "../../src/pages/forms/consultationform/proptologysections/proctologynewsection.tsx", "../../src/pages/forms/consultationform/sections/costsummary.tsx", "../../src/pages/forms/consultationform/sections/openingposition.tsx", "../../src/pages/forms/consultationform/sections/sectioneight.tsx", "../../src/pages/forms/consultationform/sections/sectionfive.tsx", "../../src/pages/forms/consultationform/sections/sectionfour.tsx", "../../src/pages/forms/consultationform/sections/sectionone.tsx", "../../src/pages/forms/consultationform/sections/sectionseven.tsx", "../../src/pages/forms/consultationform/sections/sectionsix.tsx", "../../src/pages/forms/consultationform/sections/sectionthree.tsx", "../../src/pages/forms/consultationform/sections/sectiontwo.tsx", "../../src/pages/forms/consultationform/sections/treatmentplan.tsx", "../../src/pages/forms/deparmentsform/deparmentsform/sectionone.tsx", "../../src/pages/forms/deparmentsform/deparmentsform/departementoptions.ts", "../../src/pages/forms/deparmentsform/deparmentsform/departments.tsx", "../../src/pages/forms/deparmentsform/deparmentsform/validationform.ts", "../../src/pages/forms/departmenttype/departmenttype.tsx", "../../src/pages/forms/diagnosis/diagnosis.tsx", "../../src/pages/forms/diagnosis/sectionone.tsx", "../../src/pages/forms/diagnosis/validationform.ts", "../../src/pages/forms/diet/diet.tsx", "../../src/pages/forms/diet/dietformoptions.ts", "../../src/pages/forms/diet/validationform.tsx", "../../src/pages/forms/doshaanalysisform/sectionone.tsx", "../../src/pages/forms/doshaanalysisform/doshaanalysisform.tsx", "../../src/pages/forms/doshaanalysisform/validationform.ts", "../../src/pages/forms/dre/dreform.tsx", "../../src/pages/forms/dre/dreformoptions.ts", "../../src/pages/forms/dre/validationform.tsx", "../../src/pages/forms/examinationform/sectionone.tsx", "../../src/pages/forms/examinationform/sectionthree.tsx", "../../src/pages/forms/examinationform/examination.tsx", "../../src/pages/forms/examinationform/validationform.ts", "../../src/pages/forms/expenses/expenses.tsx", "../../src/pages/forms/expenses/sectionone.tsx", "../../src/pages/forms/expenses/validationform.ts", "../../src/pages/forms/findings form/sectionone.tsx", "../../src/pages/forms/findings form/findings.tsx", "../../src/pages/forms/findings form/findingsformoptions.ts", "../../src/pages/forms/findings form/validationform.ts", "../../src/pages/forms/fistula/fistulaform.tsx", "../../src/pages/forms/fistula/fistulaformoptions.ts", "../../src/pages/forms/fistula/validationform.tsx", "../../src/pages/forms/foodadvice/foodadvice.tsx", "../../src/pages/forms/foodadvice/validationform.ts", "../../src/pages/forms/management/managementform.tsx", "../../src/pages/forms/management/validationform.ts", "../../src/pages/forms/medicinecategory/medicinecategory.tsx", "../../src/pages/forms/medicinecategory/validationform.tsx", "../../src/pages/forms/medicinecategorymapping/medicinecategorymapping.tsx", "../../src/pages/forms/medicinecategorymapping/validationform.ts", "../../src/pages/forms/medicinesform/sectionone.tsx", "../../src/pages/forms/medicinesform/sectionthree.tsx", "../../src/pages/forms/medicinesform/sectiontwo.tsx", "../../src/pages/forms/medicinesform/medicines.tsx", "../../src/pages/forms/medicinesform/medicinesformoptions.ts", "../../src/pages/forms/medicinesform/validationform.ts", "../../src/pages/forms/onexamination/onexamination.tsx", "../../src/pages/forms/onexamination/sectionone.tsx", "../../src/pages/forms/onexamination/validationform.ts", "../../src/pages/forms/opdform/sectionone.tsx", "../../src/pages/forms/opdform/opd.tsx", "../../src/pages/forms/opdform/opdformoptions.ts", "../../src/pages/forms/opdform/validationform.ts", "../../src/pages/forms/patientform/sectionfour.tsx", "../../src/pages/forms/patientform/sectionone.tsx", "../../src/pages/forms/patientform/sectionthree.tsx", "../../src/pages/forms/patientform/sectiontwo.tsx", "../../src/pages/forms/patientform/patient.tsx", "../../src/pages/forms/patientform/patientformoptions.ts", "../../src/pages/forms/patientform/validationform.ts", "../../src/pages/forms/proctoscopy/proctoscopyform.tsx", "../../src/pages/forms/proctoscopy/proctoscopyformoptions.ts", "../../src/pages/forms/proctoscopy/validationform.tsx", "../../src/pages/forms/rolesform/sectionone.tsx", "../../src/pages/forms/rolesform/appointmentformoptions.ts", "../../src/pages/forms/rolesform/role.tsx", "../../src/pages/forms/rolesform/validationform.ts", "../../src/pages/forms/roomsform/sectionone.tsx", "../../src/pages/forms/roomsform/roomformoptions.ts", "../../src/pages/forms/roomsform/rooms.tsx", "../../src/pages/forms/roomsform/validationform.ts", "../../src/pages/forms/servicecostform/servicecostform.tsx", "../../src/pages/forms/servicecostform/servicecostformoptions.ts", "../../src/pages/forms/servicecostform/validationform.ts", "../../src/pages/forms/surgicalhistory/surgicalhistory.tsx", "../../src/pages/forms/surgicalhistory/validationform.ts", "../../src/pages/forms/test/test.tsx", "../../src/pages/forms/test/validationform.ts", "../../src/pages/forms/userform/sectionone.tsx", "../../src/pages/forms/userform/sectiontwo.tsx", "../../src/pages/forms/userform/user.tsx", "../../src/pages/forms/userform/validationform.ts", "../../src/pages/forms/yogaasana/sectionone.tsx", "../../src/pages/forms/yogaasana/validationform.ts", "../../src/pages/forms/yogaasana/yogaasana.tsx", "../../src/pages/forms/yogaasana/yogaasanaformoptions.ts", "../../src/pages/invoice/invoicedetail.tsx", "../../src/pages/invoice/invoicepage.tsx", "../../src/pages/invoice/paymentsection.tsx", "../../src/pages/invoice/sectionone.tsx", "../../src/pages/invoice/servicemodel.tsx", "../../src/pages/invoice/validationform.ts", "../../src/pages/management/managementdetails.tsx", "../../src/pages/management/managementpage.tsx", "../../src/pages/medicinecategory/medicinecategorypage.tsx", "../../src/pages/medicinecategorymapping/medicinecategorymappingpage.tsx", "../../src/pages/medicines/medicinedetailspage.tsx", "../../src/pages/medicines/medicinespage.tsx", "../../src/pages/medicines copy/examinationdetailspage.tsx", "../../src/pages/medicines copy/examinationspage.tsx", "../../src/pages/onexamination/onexaminationpage.tsx", "../../src/pages/opd/opddetailpage.tsx", "../../src/pages/opd/opdpage.tsx", "../../src/pages/patien tests/patienttestdetails.tsx", "../../src/pages/patien tests/patienttestpage.tsx", "../../src/pages/patient/patientinfo.tsx", "../../src/pages/patient/patientsdetailspage.tsx", "../../src/pages/patient/patientspage.tsx", "../../src/pages/patient/postsurgeryfollowup.tsx", "../../src/pages/postsurgeryfollowup/index.tsx", "../../src/pages/proctoscopy/proctoscopydetailspage.tsx", "../../src/pages/proctoscopy/proctoscopypage.tsx", "../../src/pages/reports/expense/index.tsx", "../../src/pages/reports/invoice/index.tsx", "../../src/pages/roles/permission.tsx", "../../src/pages/roles/index.tsx", "../../src/pages/rooms/roomdetail.tsx", "../../src/pages/rooms/roomspage.tsx", "../../src/pages/servicecosts/servicecostdetail.tsx", "../../src/pages/servicecosts/servicecostspage.tsx", "../../src/pages/settings/home.tsx", "../../src/pages/surgicalhistory/surgicalhistorypage.tsx", "../../src/pages/systemsettings/colorschemesection.tsx", "../../src/pages/systemsettings/emailnotification.tsx", "../../src/pages/systemsettings/generalsettingssection.tsx", "../../src/pages/systemsettings/home.tsx", "../../src/pages/systemsettings/prefixessection.tsx", "../../src/pages/systemsettings/themesettingsection.tsx", "../../src/pages/systemsettings/systemsettingsoptions.ts", "../../src/pages/systemsettings/validationschema.ts", "../../src/pages/test/testdetail.tsx", "../../src/pages/test/testpage.tsx", "../../src/pages/users/userdetailpage.tsx", "../../src/pages/users/userspage.tsx", "../../src/pages/yogaasana/yogaasanadetail.tsx", "../../src/pages/yogaasana/yogaasanapage.tsx", "../../src/utils/colorupdater.ts", "../../src/utils/colorutils.ts", "../../src/utils/comparedata.ts", "../../src/utils/datetimeutils.ts", "../../src/utils/fetcher.tsx", "../../src/utils/helperfunctions.ts", "../../src/utils/message.ts", "../../src/utils/status.ts", "../../src/utils/statuscolorschemadecider.ts", "../../src/utils/custom-hooks/use-age-calculate.ts", "../../src/utils/custom-hooks/use-browser-dimentions.ts", "../../src/utils/custom-hooks/use-form.ts", "../../src/utils/custom-hooks/use-mobile.tsx", "../../src/utils/custom-hooks/use-options.ts", "../../src/utils/custom-hooks/use-toast.ts", "../../src/utils/custom-hooks/use-validator.ts", "../../src/utils/custom-hooks/usedateformater.ts", "../../src/utils/custom-hooks/useextractvalue.ts", "../../src/utils/urls/backend.ts", "../../src/utils/urls/frontend.ts"], "version": "5.7.3"}