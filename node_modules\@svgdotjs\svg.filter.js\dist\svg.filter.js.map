{"version": 3, "file": "svg.filter.js", "sources": ["../src/svg.filter.js"], "sourcesContent": ["import {\n  Array as <PERSON><PERSON><PERSON><PERSON>,\n  Container,\n  Defs,\n  Element,\n  extend,\n  find,\n  namespaces as ns,\n  nodeOrNew,\n  utils,\n  wrapWithAttrCheck\n} from '@svgdotjs/svg.js'\n\nexport default class Filter extends Element {\n  constructor (node) {\n    super(nodeOrNew('filter', node), node)\n\n    this.$source = 'SourceGraphic'\n    this.$sourceAlpha = 'SourceAlpha'\n    this.$background = 'BackgroundImage'\n    this.$backgroundAlpha = 'BackgroundAlpha'\n    this.$fill = 'FillPaint'\n    this.$stroke = 'StrokePaint'\n    this.$autoSetIn = true\n  }\n\n  put (element, i) {\n    element = super.put(element, i)\n\n    if (!element.attr('in') && this.$autoSetIn) {\n      element.attr('in', this.$source)\n    }\n    if (!element.attr('result')) {\n      element.attr('result', element.id())\n    }\n\n    return element\n  }\n\n  // Unmask all masked elements and remove itself\n  remove () {\n    // unmask all targets\n    this.targets().each('unfilter')\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets () {\n    return find('svg [filter*=\"' + this.id() + '\"]')\n  }\n\n  toString () {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\n// Create Effect class\nclass Effect extends Element {\n  constructor (node, attr) {\n    super(node, attr)\n    this.result(this.id())\n  }\n\n  in (effect) {\n    // Act as getter\n    if (effect == null) {\n      const _in = this.attr('in')\n      const ref = this.parent() && this.parent().find(`[result=\"${_in}\"]`)[0]\n      return ref || _in\n    }\n\n    // Avr as setter\n    return this.attr('in', effect)\n  }\n\n  // Named result\n  result (result) {\n    return this.attr('result', result)\n  }\n\n  // Stringification\n  toString () {\n    return this.result()\n  }\n}\n\n// This function takes an array with attr keys and sets for every key the\n// attribute to the value of one paramater\n// getAttrSetter(['a', 'b']) becomes this.attr({a: param1, b: param2})\nconst getAttrSetter = (params) => {\n  return function (...args) {\n    for (let i = params.length; i--;) {\n      if (args[i] != null) {\n        this.attr(params[i], args[i])\n      }\n    }\n  }\n}\n\nconst updateFunctions = {\n  blend: getAttrSetter(['in', 'in2', 'mode']),\n  // ColorMatrix effect\n  colorMatrix: getAttrSetter(['type', 'values']),\n  // Composite effect\n  composite: getAttrSetter(['in', 'in2', 'operator']),\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    matrix = new SVGArray(matrix).toString()\n\n    this.attr({\n      order: Math.sqrt(matrix.split(' ').length),\n      kernelMatrix: matrix\n    })\n  },\n  // DiffuseLighting effect\n  diffuseLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'kernelUnitLength']),\n  // DisplacementMap effect\n  displacementMap: getAttrSetter(['in', 'in2', 'scale', 'xChannelSelector', 'yChannelSelector']),\n  // DropShadow effect\n  dropShadow: getAttrSetter(['in', 'dx', 'dy', 'stdDeviation']),\n  // Flood effect\n  flood: getAttrSetter(['flood-color', 'flood-opacity']),\n  // Gaussian Blur effect\n  gaussianBlur: function (x = 0, y = x) {\n    this.attr('stdDeviation', x + ' ' + y)\n  },\n  // Image effect\n  image: function (src) {\n    this.attr('href', src, ns.xlink)\n  },\n  // Morphology effect\n  morphology: getAttrSetter(['operator', 'radius']),\n  // Offset effect\n  offset: getAttrSetter(['dx', 'dy']),\n  // SpecularLighting effect\n  specularLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'specularExponent', 'kernelUnitLength']),\n  // Tile effect\n  tile: getAttrSetter([]),\n  // Turbulence effect\n  turbulence: getAttrSetter(['baseFrequency', 'numOctaves', 'seed', 'stitchTiles', 'type'])\n}\n\nconst filterNames = [\n  'blend',\n  'colorMatrix',\n  'componentTransfer',\n  'composite',\n  'convolveMatrix',\n  'diffuseLighting',\n  'displacementMap',\n  'dropShadow',\n  'flood',\n  'gaussianBlur',\n  'image',\n  'merge',\n  'morphology',\n  'offset',\n  'specularLighting',\n  'tile',\n  'turbulence'\n]\n\n// For every filter create a class\nfilterNames.forEach((effect) => {\n  const name = utils.capitalize(effect)\n  const fn = updateFunctions[effect]\n\n  Filter[name + 'Effect'] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n\n    // This function takes all parameters from the factory call\n    // and updates the attributes according to the updateFunctions\n    update (args) {\n      fn.apply(this, args)\n      return this\n    }\n  }\n\n  // Add factory function to filter\n  // Allow to pass a function or object\n  // The attr object is catched from \"wrapWithAttrCheck\"\n  Filter.prototype[effect] = wrapWithAttrCheck(function (fn, ...args) {\n    const effect = new Filter[name + 'Effect']()\n\n    if (fn == null) return this.put(effect)\n\n    // For Effects which can take children, a function is allowed\n    if (typeof fn === 'function') {\n      fn.call(effect, effect)\n    } else {\n      // In case it is not a function, add it to arguments\n      args.unshift(fn)\n    }\n    return this.put(effect).update(args)\n  })\n})\n\n// Correct factories which are not that simple\nextend(Filter, {\n  merge (arrayOrFn) {\n    const node = this.put(new Filter.MergeEffect())\n\n    // If a function was passed, execute it\n    // That makes stuff like this possible:\n    // filter.merge((mergeEffect) => mergeEffect.mergeNode(in))\n    if (typeof arrayOrFn === 'function') {\n      arrayOrFn.call(node, node)\n      return node\n    }\n\n    // Check if first child is an array, otherwise use arguments as array\n    const children = arrayOrFn instanceof Array ? arrayOrFn : [...arguments]\n\n    children.forEach((child) => {\n      if (child instanceof Filter.MergeNode) {\n        node.put(child)\n      } else {\n        node.mergeNode(child)\n      }\n    })\n\n    return node\n  },\n  componentTransfer (components = {}) {\n    const node = this.put(new Filter.ComponentTransferEffect())\n\n    if (typeof components === 'function') {\n      components.call(node, node)\n      return node\n    }\n\n    // If no component is set, we use the given object for all components\n    if (!components.r && !components.g && !components.b && !components.a) {\n      const temp = components\n      components = {\n        r: temp, g: temp, b: temp, a: temp\n      }\n    }\n\n    for (const c in components) {\n      // components[c] has to hold an attributes object\n      node.add(new Filter['Func' + c.toUpperCase()](components[c]))\n    }\n\n    return node\n  }\n})\n\nconst filterChildNodes = [\n  'distantLight',\n  'pointLight',\n  'spotLight',\n  'mergeNode',\n  'FuncR',\n  'FuncG',\n  'FuncB',\n  'FuncA'\n]\n\nfilterChildNodes.forEach((child) => {\n  const name = utils.capitalize(child)\n  Filter[name] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n  }\n})\n\nconst componentFuncs = [\n  'funcR',\n  'funcG',\n  'funcB',\n  'funcA'\n]\n\n// Add an update function for componentTransfer-children\ncomponentFuncs.forEach(function (c) {\n  const _class = Filter[utils.capitalize(c)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.ComponentTransferEffect.prototype[c] = fn\n})\n\nconst lights = [\n  'distantLight',\n  'pointLight',\n  'spotLight'\n]\n\n// Add light sources factories to lightining effects\nlights.forEach((light) => {\n  const _class = Filter[utils.capitalize(light)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.DiffuseLightingEffect.prototype[light] = fn\n  Filter.SpecularLightingEffect.prototype[light] = fn\n})\n\nextend(Filter.MergeEffect, {\n  mergeNode (_in) {\n    return this.put(new Filter.MergeNode()).attr('in', _in)\n  }\n})\n\n// add .filter function\nextend(Defs, {\n  // Define filter\n  filter: function (block) {\n    const filter = this.put(new Filter())\n\n    /* invoke passed block */\n    if (typeof block === 'function') { block.call(filter, filter) }\n\n    return filter\n  }\n})\n\nextend(Container, {\n  // Define filter on defs\n  filter: function (block) {\n    return this.defs().filter(block)\n  }\n})\n\nextend(Element, {\n  // Create filter element in defs and store reference\n  filterWith: function (block) {\n    const filter = block instanceof Filter\n      ? block\n      : this.defs().filter(block)\n\n    return this.attr('filter', filter)\n  },\n  // Remove filter\n  unfilter: function (remove) {\n    /* remove filter attribute */\n    return this.attr('filter', null)\n  },\n  filterer () {\n    return this.reference('filter')\n  }\n})\n\n// chaining\nconst chainingEffects = {\n  // Blend effect\n  blend: function (in2, mode) {\n    return this.parent() && this.parent().blend(this, in2, mode) // pass this as the first input\n  },\n  // ColorMatrix effect\n  colorMatrix: function (type, values) {\n    return this.parent() && this.parent().colorMatrix(type, values).in(this)\n  },\n  // ComponentTransfer effect\n  componentTransfer: function (components) {\n    return this.parent() && this.parent().componentTransfer(components).in(this)\n  },\n  // Composite effect\n  composite: function (in2, operator) {\n    return this.parent() && this.parent().composite(this, in2, operator) // pass this as the first input\n  },\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    return this.parent() && this.parent().convolveMatrix(matrix).in(this)\n  },\n  // DiffuseLighting effect\n  diffuseLighting: function (surfaceScale, lightingColor, diffuseConstant, kernelUnitLength) {\n    return this.parent() && this.parent().diffuseLighting(surfaceScale, diffuseConstant, kernelUnitLength).in(this)\n  },\n  // DisplacementMap effect\n  displacementMap: function (in2, scale, xChannelSelector, yChannelSelector) {\n    return this.parent() && this.parent().displacementMap(this, in2, scale, xChannelSelector, yChannelSelector) // pass this as the first input\n  },\n  // DisplacementMap effect\n  dropShadow: function (x, y, stdDeviation) {\n    return this.parent() && this.parent().dropShadow(this, x, y, stdDeviation).in(this) // pass this as the first input\n  },\n  // Flood effect\n  flood: function (color, opacity) {\n    return this.parent() && this.parent().flood(color, opacity) // this effect dont have inputs\n  },\n  // Gaussian Blur effect\n  gaussianBlur: function (x, y) {\n    return this.parent() && this.parent().gaussianBlur(x, y).in(this)\n  },\n  // Image effect\n  image: function (src) {\n    return this.parent() && this.parent().image(src) // this effect dont have inputs\n  },\n  // Merge effect\n  merge: function (arg) {\n    arg = arg instanceof Array ? arg : [...arg]\n    return this.parent() && this.parent().merge(this, ...arg) // pass this as the first argument\n  },\n  // Morphology effect\n  morphology: function (operator, radius) {\n    return this.parent() && this.parent().morphology(operator, radius).in(this)\n  },\n  // Offset effect\n  offset: function (dx, dy) {\n    return this.parent() && this.parent().offset(dx, dy).in(this)\n  },\n  // SpecularLighting effect\n  specularLighting: function (surfaceScale, lightingColor, diffuseConstant, specularExponent, kernelUnitLength) {\n    return this.parent() && this.parent().specularLighting(surfaceScale, diffuseConstant, specularExponent, kernelUnitLength).in(this)\n  },\n  // Tile effect\n  tile: function () {\n    return this.parent() && this.parent().tile().in(this)\n  },\n  // Turbulence effect\n  turbulence: function (baseFrequency, numOctaves, seed, stitchTiles, type) {\n    return this.parent() && this.parent().turbulence(baseFrequency, numOctaves, seed, stitchTiles, type).in(this)\n  }\n}\n\nextend(Effect, chainingEffects)\n\n// Effect-specific extensions\nextend(Filter.MergeEffect, {\n  in: function (effect) {\n    if (effect instanceof Filter.MergeNode) {\n      this.add(effect, 0)\n    } else {\n      this.add(new Filter.MergeNode().in(effect), 0)\n    }\n\n    return this\n  }\n})\n\nextend([Filter.CompositeEffect, Filter.BlendEffect, Filter.DisplacementMapEffect], {\n  in2: function (effect) {\n    if (effect == null) {\n      const in2 = this.attr('in2')\n      const ref = this.parent() && this.parent().find(`[result=\"${in2}\"]`)[0]\n      return ref || in2\n    }\n    return this.attr('in2', effect)\n  }\n})\n\n// Presets\nFilter.filter = {\n  sepiatone: [\n    0.343, 0.669, 0.119, 0, 0,\n    0.249, 0.626, 0.130, 0, 0,\n    0.172, 0.334, 0.111, 0, 0,\n    0.000, 0.000, 0.000, 1, 0]\n}\n"], "names": ["Filter", "Element", "constructor", "node", "nodeOrNew", "$source", "$sourceAlpha", "$background", "$backgroundAlpha", "$fill", "$stroke", "$autoSetIn", "put", "element", "i", "attr", "id", "remove", "targets", "each", "find", "toString", "Effect", "result", "in", "effect", "_in", "ref", "parent", "getAttrSetter", "params", "args", "length", "updateFunctions", "blend", "colorMatrix", "composite", "convolveMatrix", "matrix", "SVGArray", "order", "Math", "sqrt", "split", "kernelMatrix", "diffuseLighting", "displacementMap", "dropShadow", "flood", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "image", "src", "ns", "xlink", "morphology", "offset", "specularLighting", "tile", "turbulence", "filterNames", "for<PERSON>ach", "name", "utils", "capitalize", "fn", "update", "apply", "prototype", "wrapWithAttrCheck", "call", "unshift", "extend", "merge", "arrayOrFn", "MergeEffect", "children", "Array", "arguments", "child", "MergeNode", "mergeNode", "componentTransfer", "components", "ComponentTransferEffect", "r", "g", "b", "a", "temp", "c", "add", "toUpperCase", "filterChildNodes", "componentFuncs", "_class", "lights", "light", "DiffuseLightingEffect", "SpecularLightingEffect", "Defs", "filter", "block", "Container", "defs", "filterWith", "unfilter", "filterer", "reference", "chainingEffects", "in2", "mode", "type", "values", "operator", "surfaceScale", "lightingColor", "diffuseConstant", "kernelUnitLength", "scale", "xChannelSelector", "yChannelSelector", "stdDeviation", "color", "opacity", "arg", "radius", "dx", "dy", "specularExponent", "baseFrequency", "numOctaves", "seed", "stitchTiles", "CompositeEffect", "BlendEffect", "DisplacementMapEffect", "sepiatone"], "mappings": ";;;;;;;;;;;;;;EAae,MAAMA,MAAM,SAASC,cAAO,CAAC;IAC1CC,WAAWA,CAAEC,IAAI,EAAE;MACjB,KAAK,CAACC,gBAAS,CAAC,QAAQ,EAAED,IAAI,CAAC,EAAEA,IAAI,CAAC;MAEtC,IAAI,CAACE,OAAO,GAAG,eAAe;MAC9B,IAAI,CAACC,YAAY,GAAG,aAAa;MACjC,IAAI,CAACC,WAAW,GAAG,iBAAiB;MACpC,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;MACzC,IAAI,CAACC,KAAK,GAAG,WAAW;MACxB,IAAI,CAACC,OAAO,GAAG,aAAa;MAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;EACxB;EAEAC,EAAAA,GAAGA,CAAEC,OAAO,EAAEC,CAAC,EAAE;MACfD,OAAO,GAAG,KAAK,CAACD,GAAG,CAACC,OAAO,EAAEC,CAAC,CAAC;MAE/B,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAACJ,UAAU,EAAE;QAC1CE,OAAO,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,CAACV,OAAO,CAAC;EAClC;EACA,IAAA,IAAI,CAACQ,OAAO,CAACE,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC3BF,OAAO,CAACE,IAAI,CAAC,QAAQ,EAAEF,OAAO,CAACG,EAAE,EAAE,CAAC;EACtC;EAEA,IAAA,OAAOH,OAAO;EAChB;;EAEA;EACAI,EAAAA,MAAMA,GAAI;EACR;MACA,IAAI,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,UAAU,CAAC;;EAE/B;EACA,IAAA,OAAO,KAAK,CAACF,MAAM,EAAE;EACvB;EAEAC,EAAAA,OAAOA,GAAI;MACT,OAAOE,WAAI,CAAC,gBAAgB,GAAG,IAAI,CAACJ,EAAE,EAAE,GAAG,IAAI,CAAC;EAClD;EAEAK,EAAAA,QAAQA,GAAI;MACV,OAAO,OAAO,GAAG,IAAI,CAACL,EAAE,EAAE,GAAG,GAAG;EAClC;EACF;;EAEA;EACA,MAAMM,MAAM,SAASrB,cAAO,CAAC;EAC3BC,EAAAA,WAAWA,CAAEC,IAAI,EAAEY,IAAI,EAAE;EACvB,IAAA,KAAK,CAACZ,IAAI,EAAEY,IAAI,CAAC;MACjB,IAAI,CAACQ,MAAM,CAAC,IAAI,CAACP,EAAE,EAAE,CAAC;EACxB;IAEAQ,EAAEA,CAAEC,MAAM,EAAE;EACV;MACA,IAAIA,MAAM,IAAI,IAAI,EAAE;EAClB,MAAA,MAAMC,GAAG,GAAG,IAAI,CAACX,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAMY,GAAG,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACR,IAAI,CAAC,CAAA,SAAA,EAAYM,GAAG,CAAI,EAAA,CAAA,CAAC,CAAC,CAAC,CAAC;QACvE,OAAOC,GAAG,IAAID,GAAG;EACnB;;EAEA;EACA,IAAA,OAAO,IAAI,CAACX,IAAI,CAAC,IAAI,EAAEU,MAAM,CAAC;EAChC;;EAEA;IACAF,MAAMA,CAAEA,MAAM,EAAE;EACd,IAAA,OAAO,IAAI,CAACR,IAAI,CAAC,QAAQ,EAAEQ,MAAM,CAAC;EACpC;;EAEA;EACAF,EAAAA,QAAQA,GAAI;EACV,IAAA,OAAO,IAAI,CAACE,MAAM,EAAE;EACtB;EACF;;EAEA;EACA;EACA;EACA,MAAMM,aAAa,GAAIC,MAAM,IAAK;IAChC,OAAO,UAAU,GAAGC,IAAI,EAAE;MACxB,KAAK,IAAIjB,CAAC,GAAGgB,MAAM,CAACE,MAAM,EAAElB,CAAC,EAAE,GAAG;EAChC,MAAA,IAAIiB,IAAI,CAACjB,CAAC,CAAC,IAAI,IAAI,EAAE;EACnB,QAAA,IAAI,CAACC,IAAI,CAACe,MAAM,CAAChB,CAAC,CAAC,EAAEiB,IAAI,CAACjB,CAAC,CAAC,CAAC;EAC/B;EACF;KACD;EACH,CAAC;EAED,MAAMmB,eAAe,GAAG;IACtBC,KAAK,EAAEL,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EAC3C;IACAM,WAAW,EAAEN,aAAa,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;EAC9C;IACAO,SAAS,EAAEP,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;EACnD;EACAQ,EAAAA,cAAc,EAAE,UAAUC,MAAM,EAAE;MAChCA,MAAM,GAAG,IAAIC,YAAQ,CAACD,MAAM,CAAC,CAACjB,QAAQ,EAAE;MAExC,IAAI,CAACN,IAAI,CAAC;EACRyB,MAAAA,KAAK,EAAEC,IAAI,CAACC,IAAI,CAACJ,MAAM,CAACK,KAAK,CAAC,GAAG,CAAC,CAACX,MAAM,CAAC;EAC1CY,MAAAA,YAAY,EAAEN;EAChB,KAAC,CAAC;KACH;EACD;EACAO,EAAAA,eAAe,EAAEhB,aAAa,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;EACxG;EACAiB,EAAAA,eAAe,EAAEjB,aAAa,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;EAC9F;EACAkB,EAAAA,UAAU,EAAElB,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;EAC7D;IACAmB,KAAK,EAAEnB,aAAa,CAAC,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;EACtD;IACAoB,YAAY,EAAE,UAAUC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAE;MACpC,IAAI,CAACnC,IAAI,CAAC,cAAc,EAAEmC,CAAC,GAAG,GAAG,GAAGC,CAAC,CAAC;KACvC;EACD;EACAC,EAAAA,KAAK,EAAE,UAAUC,GAAG,EAAE;MACpB,IAAI,CAACtC,IAAI,CAAC,MAAM,EAAEsC,GAAG,EAAEC,iBAAE,CAACC,KAAK,CAAC;KACjC;EACD;IACAC,UAAU,EAAE3B,aAAa,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EACjD;IACA4B,MAAM,EAAE5B,aAAa,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EACnC;EACA6B,EAAAA,gBAAgB,EAAE7B,aAAa,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;EAC7H;EACA8B,EAAAA,IAAI,EAAE9B,aAAa,CAAC,EAAE,CAAC;EACvB;EACA+B,EAAAA,UAAU,EAAE/B,aAAa,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,CAAC;EAC1F,CAAC;EAED,MAAMgC,WAAW,GAAG,CAClB,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,OAAO,EACP,cAAc,EACd,OAAO,EACP,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,kBAAkB,EAClB,MAAM,EACN,YAAY,CACb;;EAED;EACAA,WAAW,CAACC,OAAO,CAAErC,MAAM,IAAK;EAC9B,EAAA,MAAMsC,IAAI,GAAGC,YAAK,CAACC,UAAU,CAACxC,MAAM,CAAC;EACrC,EAAA,MAAMyC,EAAE,GAAGjC,eAAe,CAACR,MAAM,CAAC;IAElCzB,MAAM,CAAC+D,IAAI,GAAG,QAAQ,CAAC,GAAG,cAAczC,MAAM,CAAC;MAC7CpB,WAAWA,CAAEC,IAAI,EAAE;QACjB,KAAK,CAACC,gBAAS,CAAC,IAAI,GAAG2D,IAAI,EAAE5D,IAAI,CAAC,EAAEA,IAAI,CAAC;EAC3C;;EAEA;EACA;MACAgE,MAAMA,CAAEpC,IAAI,EAAE;EACZmC,MAAAA,EAAE,CAACE,KAAK,CAAC,IAAI,EAAErC,IAAI,CAAC;EACpB,MAAA,OAAO,IAAI;EACb;KACD;;EAED;EACA;EACA;EACA/B,EAAAA,MAAM,CAACqE,SAAS,CAAC5C,MAAM,CAAC,GAAG6C,wBAAiB,CAAC,UAAUJ,EAAE,EAAE,GAAGnC,IAAI,EAAE;MAClE,MAAMN,MAAM,GAAG,IAAIzB,MAAM,CAAC+D,IAAI,GAAG,QAAQ,CAAC,EAAE;MAE5C,IAAIG,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,CAACtD,GAAG,CAACa,MAAM,CAAC;;EAEvC;EACA,IAAA,IAAI,OAAOyC,EAAE,KAAK,UAAU,EAAE;EAC5BA,MAAAA,EAAE,CAACK,IAAI,CAAC9C,MAAM,EAAEA,MAAM,CAAC;EACzB,KAAC,MAAM;EACL;EACAM,MAAAA,IAAI,CAACyC,OAAO,CAACN,EAAE,CAAC;EAClB;MACA,OAAO,IAAI,CAACtD,GAAG,CAACa,MAAM,CAAC,CAAC0C,MAAM,CAACpC,IAAI,CAAC;EACtC,GAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;AACA0C,eAAM,CAACzE,MAAM,EAAE;IACb0E,KAAKA,CAAEC,SAAS,EAAE;EAChB,IAAA,MAAMxE,IAAI,GAAG,IAAI,CAACS,GAAG,CAAC,IAAIZ,MAAM,CAAC4E,WAAW,EAAE,CAAC;;EAE/C;EACA;EACA;EACA,IAAA,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;EACnCA,MAAAA,SAAS,CAACJ,IAAI,CAACpE,IAAI,EAAEA,IAAI,CAAC;EAC1B,MAAA,OAAOA,IAAI;EACb;;EAEA;MACA,MAAM0E,QAAQ,GAAGF,SAAS,YAAYG,KAAK,GAAGH,SAAS,GAAG,CAAC,GAAGI,SAAS,CAAC;EAExEF,IAAAA,QAAQ,CAACf,OAAO,CAAEkB,KAAK,IAAK;EAC1B,MAAA,IAAIA,KAAK,YAAYhF,MAAM,CAACiF,SAAS,EAAE;EACrC9E,QAAAA,IAAI,CAACS,GAAG,CAACoE,KAAK,CAAC;EACjB,OAAC,MAAM;EACL7E,QAAAA,IAAI,CAAC+E,SAAS,CAACF,KAAK,CAAC;EACvB;EACF,KAAC,CAAC;EAEF,IAAA,OAAO7E,IAAI;KACZ;EACDgF,EAAAA,iBAAiBA,CAAEC,UAAU,GAAG,EAAE,EAAE;EAClC,IAAA,MAAMjF,IAAI,GAAG,IAAI,CAACS,GAAG,CAAC,IAAIZ,MAAM,CAACqF,uBAAuB,EAAE,CAAC;EAE3D,IAAA,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;EACpCA,MAAAA,UAAU,CAACb,IAAI,CAACpE,IAAI,EAAEA,IAAI,CAAC;EAC3B,MAAA,OAAOA,IAAI;EACb;;EAEA;EACA,IAAA,IAAI,CAACiF,UAAU,CAACE,CAAC,IAAI,CAACF,UAAU,CAACG,CAAC,IAAI,CAACH,UAAU,CAACI,CAAC,IAAI,CAACJ,UAAU,CAACK,CAAC,EAAE;QACpE,MAAMC,IAAI,GAAGN,UAAU;EACvBA,MAAAA,UAAU,GAAG;EACXE,QAAAA,CAAC,EAAEI,IAAI;EAAEH,QAAAA,CAAC,EAAEG,IAAI;EAAEF,QAAAA,CAAC,EAAEE,IAAI;EAAED,QAAAA,CAAC,EAAEC;SAC/B;EACH;EAEA,IAAA,KAAK,MAAMC,CAAC,IAAIP,UAAU,EAAE;EAC1B;QACAjF,IAAI,CAACyF,GAAG,CAAC,IAAI5F,MAAM,CAAC,MAAM,GAAG2F,CAAC,CAACE,WAAW,EAAE,CAAC,CAACT,UAAU,CAACO,CAAC,CAAC,CAAC,CAAC;EAC/D;EAEA,IAAA,OAAOxF,IAAI;EACb;EACF,CAAC,CAAC;EAEF,MAAM2F,gBAAgB,GAAG,CACvB,cAAc,EACd,YAAY,EACZ,WAAW,EACX,WAAW,EACX,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;EAEDA,gBAAgB,CAAChC,OAAO,CAAEkB,KAAK,IAAK;EAClC,EAAA,MAAMjB,IAAI,GAAGC,YAAK,CAACC,UAAU,CAACe,KAAK,CAAC;EACpChF,EAAAA,MAAM,CAAC+D,IAAI,CAAC,GAAG,cAAczC,MAAM,CAAC;MAClCpB,WAAWA,CAAEC,IAAI,EAAE;QACjB,KAAK,CAACC,gBAAS,CAAC,IAAI,GAAG2D,IAAI,EAAE5D,IAAI,CAAC,EAAEA,IAAI,CAAC;EAC3C;KACD;EACH,CAAC,CAAC;EAEF,MAAM4F,cAAc,GAAG,CACrB,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;;EAED;EACAA,cAAc,CAACjC,OAAO,CAAC,UAAU6B,CAAC,EAAE;IAClC,MAAMK,MAAM,GAAGhG,MAAM,CAACgE,YAAK,CAACC,UAAU,CAAC0B,CAAC,CAAC,CAAC;EAC1C,EAAA,MAAMzB,EAAE,GAAGI,wBAAiB,CAAC,YAAY;MACvC,OAAO,IAAI,CAAC1D,GAAG,CAAC,IAAIoF,MAAM,EAAE,CAAC;EAC/B,GAAC,CAAC;IAEFhG,MAAM,CAACqF,uBAAuB,CAAChB,SAAS,CAACsB,CAAC,CAAC,GAAGzB,EAAE;EAClD,CAAC,CAAC;EAEF,MAAM+B,MAAM,GAAG,CACb,cAAc,EACd,YAAY,EACZ,WAAW,CACZ;;EAED;EACAA,MAAM,CAACnC,OAAO,CAAEoC,KAAK,IAAK;IACxB,MAAMF,MAAM,GAAGhG,MAAM,CAACgE,YAAK,CAACC,UAAU,CAACiC,KAAK,CAAC,CAAC;EAC9C,EAAA,MAAMhC,EAAE,GAAGI,wBAAiB,CAAC,YAAY;MACvC,OAAO,IAAI,CAAC1D,GAAG,CAAC,IAAIoF,MAAM,EAAE,CAAC;EAC/B,GAAC,CAAC;IAEFhG,MAAM,CAACmG,qBAAqB,CAAC9B,SAAS,CAAC6B,KAAK,CAAC,GAAGhC,EAAE;IAClDlE,MAAM,CAACoG,sBAAsB,CAAC/B,SAAS,CAAC6B,KAAK,CAAC,GAAGhC,EAAE;EACrD,CAAC,CAAC;AAEFO,eAAM,CAACzE,MAAM,CAAC4E,WAAW,EAAE;IACzBM,SAASA,CAAExD,GAAG,EAAE;EACd,IAAA,OAAO,IAAI,CAACd,GAAG,CAAC,IAAIZ,MAAM,CAACiF,SAAS,EAAE,CAAC,CAAClE,IAAI,CAAC,IAAI,EAAEW,GAAG,CAAC;EACzD;EACF,CAAC,CAAC;;EAEF;AACA+C,eAAM,CAAC4B,WAAI,EAAE;EACX;EACAC,EAAAA,MAAM,EAAE,UAAUC,KAAK,EAAE;MACvB,MAAMD,MAAM,GAAG,IAAI,CAAC1F,GAAG,CAAC,IAAIZ,MAAM,EAAE,CAAC;;EAErC;EACA,IAAA,IAAI,OAAOuG,KAAK,KAAK,UAAU,EAAE;EAAEA,MAAAA,KAAK,CAAChC,IAAI,CAAC+B,MAAM,EAAEA,MAAM,CAAC;EAAC;EAE9D,IAAA,OAAOA,MAAM;EACf;EACF,CAAC,CAAC;AAEF7B,eAAM,CAAC+B,gBAAS,EAAE;EAChB;EACAF,EAAAA,MAAM,EAAE,UAAUC,KAAK,EAAE;MACvB,OAAO,IAAI,CAACE,IAAI,EAAE,CAACH,MAAM,CAACC,KAAK,CAAC;EAClC;EACF,CAAC,CAAC;AAEF9B,eAAM,CAACxE,cAAO,EAAE;EACd;EACAyG,EAAAA,UAAU,EAAE,UAAUH,KAAK,EAAE;EAC3B,IAAA,MAAMD,MAAM,GAAGC,KAAK,YAAYvG,MAAM,GAClCuG,KAAK,GACL,IAAI,CAACE,IAAI,EAAE,CAACH,MAAM,CAACC,KAAK,CAAC;EAE7B,IAAA,OAAO,IAAI,CAACxF,IAAI,CAAC,QAAQ,EAAEuF,MAAM,CAAC;KACnC;EACD;EACAK,EAAAA,QAAQ,EAAE,UAAU1F,MAAM,EAAE;EAC1B;EACA,IAAA,OAAO,IAAI,CAACF,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;KACjC;EACD6F,EAAAA,QAAQA,GAAI;EACV,IAAA,OAAO,IAAI,CAACC,SAAS,CAAC,QAAQ,CAAC;EACjC;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG;EACtB;EACA5E,EAAAA,KAAK,EAAE,UAAU6E,GAAG,EAAEC,IAAI,EAAE;MAC1B,OAAO,IAAI,CAACpF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACM,KAAK,CAAC,IAAI,EAAE6E,GAAG,EAAEC,IAAI,CAAC,CAAC;KAC9D;EACD;EACA7E,EAAAA,WAAW,EAAE,UAAU8E,IAAI,EAAEC,MAAM,EAAE;MACnC,OAAO,IAAI,CAACtF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACO,WAAW,CAAC8E,IAAI,EAAEC,MAAM,CAAC,CAAC1F,EAAE,CAAC,IAAI,CAAC;KACzE;EACD;EACA2D,EAAAA,iBAAiB,EAAE,UAAUC,UAAU,EAAE;MACvC,OAAO,IAAI,CAACxD,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACuD,iBAAiB,CAACC,UAAU,CAAC,CAAC5D,EAAE,CAAC,IAAI,CAAC;KAC7E;EACD;EACAY,EAAAA,SAAS,EAAE,UAAU2E,GAAG,EAAEI,QAAQ,EAAE;MAClC,OAAO,IAAI,CAACvF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACQ,SAAS,CAAC,IAAI,EAAE2E,GAAG,EAAEI,QAAQ,CAAC,CAAC;KACtE;EACD;EACA9E,EAAAA,cAAc,EAAE,UAAUC,MAAM,EAAE;MAChC,OAAO,IAAI,CAACV,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACS,cAAc,CAACC,MAAM,CAAC,CAACd,EAAE,CAAC,IAAI,CAAC;KACtE;EACD;IACAqB,eAAe,EAAE,UAAUuE,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;MACzF,OAAO,IAAI,CAAC3F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACiB,eAAe,CAACuE,YAAY,EAAEE,eAAe,EAAEC,gBAAgB,CAAC,CAAC/F,EAAE,CAAC,IAAI,CAAC;KAChH;EACD;IACAsB,eAAe,EAAE,UAAUiE,GAAG,EAAES,KAAK,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAE;MACzE,OAAO,IAAI,CAAC9F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACkB,eAAe,CAAC,IAAI,EAAEiE,GAAG,EAAES,KAAK,EAAEC,gBAAgB,EAAEC,gBAAgB,CAAC,CAAC;KAC7G;EACD;IACA3E,UAAU,EAAE,UAAUG,CAAC,EAAEC,CAAC,EAAEwE,YAAY,EAAE;MACxC,OAAO,IAAI,CAAC/F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACmB,UAAU,CAAC,IAAI,EAAEG,CAAC,EAAEC,CAAC,EAAEwE,YAAY,CAAC,CAACnG,EAAE,CAAC,IAAI,CAAC,CAAC;KACrF;EACD;EACAwB,EAAAA,KAAK,EAAE,UAAU4E,KAAK,EAAEC,OAAO,EAAE;EAC/B,IAAA,OAAO,IAAI,CAACjG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACoB,KAAK,CAAC4E,KAAK,EAAEC,OAAO,CAAC,CAAC;KAC7D;EACD;EACA5E,EAAAA,YAAY,EAAE,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACvB,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACqB,YAAY,CAACC,CAAC,EAAEC,CAAC,CAAC,CAAC3B,EAAE,CAAC,IAAI,CAAC;KAClE;EACD;EACA4B,EAAAA,KAAK,EAAE,UAAUC,GAAG,EAAE;EACpB,IAAA,OAAO,IAAI,CAACzB,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACwB,KAAK,CAACC,GAAG,CAAC,CAAC;KAClD;EACD;EACAqB,EAAAA,KAAK,EAAE,UAAUoD,GAAG,EAAE;MACpBA,GAAG,GAAGA,GAAG,YAAYhD,KAAK,GAAGgD,GAAG,GAAG,CAAC,GAAGA,GAAG,CAAC;EAC3C,IAAA,OAAO,IAAI,CAAClG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC8C,KAAK,CAAC,IAAI,EAAE,GAAGoD,GAAG,CAAC,CAAC;KAC3D;EACD;EACAtE,EAAAA,UAAU,EAAE,UAAU2D,QAAQ,EAAEY,MAAM,EAAE;MACtC,OAAO,IAAI,CAACnG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC4B,UAAU,CAAC2D,QAAQ,EAAEY,MAAM,CAAC,CAACvG,EAAE,CAAC,IAAI,CAAC;KAC5E;EACD;EACAiC,EAAAA,MAAM,EAAE,UAAUuE,EAAE,EAAEC,EAAE,EAAE;MACxB,OAAO,IAAI,CAACrG,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC6B,MAAM,CAACuE,EAAE,EAAEC,EAAE,CAAC,CAACzG,EAAE,CAAC,IAAI,CAAC;KAC9D;EACD;EACAkC,EAAAA,gBAAgB,EAAE,UAAU0D,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEY,gBAAgB,EAAEX,gBAAgB,EAAE;MAC5G,OAAO,IAAI,CAAC3F,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC8B,gBAAgB,CAAC0D,YAAY,EAAEE,eAAe,EAAEY,gBAAgB,EAAEX,gBAAgB,CAAC,CAAC/F,EAAE,CAAC,IAAI,CAAC;KACnI;EACD;IACAmC,IAAI,EAAE,YAAY;EAChB,IAAA,OAAO,IAAI,CAAC/B,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAAC+B,IAAI,EAAE,CAACnC,EAAE,CAAC,IAAI,CAAC;KACtD;EACD;EACAoC,EAAAA,UAAU,EAAE,UAAUuE,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAErB,IAAI,EAAE;EACxE,IAAA,OAAO,IAAI,CAACrF,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACgC,UAAU,CAACuE,aAAa,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAErB,IAAI,CAAC,CAACzF,EAAE,CAAC,IAAI,CAAC;EAC/G;EACF,CAAC;AAEDiD,eAAM,CAACnD,MAAM,EAAEwF,eAAe,CAAC;;EAE/B;AACArC,eAAM,CAACzE,MAAM,CAAC4E,WAAW,EAAE;EACzBpD,EAAAA,EAAE,EAAE,UAAUC,MAAM,EAAE;EACpB,IAAA,IAAIA,MAAM,YAAYzB,MAAM,CAACiF,SAAS,EAAE;EACtC,MAAA,IAAI,CAACW,GAAG,CAACnE,MAAM,EAAE,CAAC,CAAC;EACrB,KAAC,MAAM;EACL,MAAA,IAAI,CAACmE,GAAG,CAAC,IAAI5F,MAAM,CAACiF,SAAS,EAAE,CAACzD,EAAE,CAACC,MAAM,CAAC,EAAE,CAAC,CAAC;EAChD;EAEA,IAAA,OAAO,IAAI;EACb;EACF,CAAC,CAAC;AAEFgD,eAAM,CAAC,CAACzE,MAAM,CAACuI,eAAe,EAAEvI,MAAM,CAACwI,WAAW,EAAExI,MAAM,CAACyI,qBAAqB,CAAC,EAAE;EACjF1B,EAAAA,GAAG,EAAE,UAAUtF,MAAM,EAAE;MACrB,IAAIA,MAAM,IAAI,IAAI,EAAE;EAClB,MAAA,MAAMsF,GAAG,GAAG,IAAI,CAAChG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAMY,GAAG,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,IAAI,CAACA,MAAM,EAAE,CAACR,IAAI,CAAC,CAAA,SAAA,EAAY2F,GAAG,CAAI,EAAA,CAAA,CAAC,CAAC,CAAC,CAAC;QACvE,OAAOpF,GAAG,IAAIoF,GAAG;EACnB;EACA,IAAA,OAAO,IAAI,CAAChG,IAAI,CAAC,KAAK,EAAEU,MAAM,CAAC;EACjC;EACF,CAAC,CAAC;;EAEF;EACAzB,MAAM,CAACsG,MAAM,GAAG;EACdoC,EAAAA,SAAS,EAAE,CACT,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EACzB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;EAC7B,CAAC;;;;;;;;"}