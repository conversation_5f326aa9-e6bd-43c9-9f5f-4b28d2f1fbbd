import { DropdownProps } from "@/interfaces/components/input/dropdownProps";
import { useState, useRef, useEffect } from "react";
import View from "./view";
import Text from "./text";

const Dropdown: React.FC<DropdownProps> = ({
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Select an option",
  value,
  defaultValue,
  dropdownSize = "medium",
  variant = "default",
  fullWidth = false,
  id,
  name,
  style,
  className,
  options = [],
  leftIcon,
  rightIcon,
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || defaultValue || "");
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        if (onBlur) onBlur();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onBlur]);

  const handleOptionClick = (optionValue: string) => {
    if (disabled) return;
    
    setSelectedValue(optionValue);
    setIsOpen(false);
    
    if (onChange) {
      onChange(optionValue);
    }
  };

  const selectedLabel = options.find(option => option.value === selectedValue)?.label || placeholder;

  return (
    <View
      ref={dropdownRef}
      className={`relative ${fullWidth ? "w-full" : ""} ${disabled ? "opacity-60 cursor-not-allowed" : ""}`}
      style={style}
    >
      <View
        className={`
          flex items-center justify-between w-full px-3 py-2 text-sm
          bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600
          rounded-lg cursor-pointer transition-all duration-200
          hover:border-primary-500 dark:hover:border-primary-400
          focus:outline-none focus:ring-2 focus:ring-primary-500/20 dark:focus:ring-primary-400/20
          ${isOpen ? 'border-primary-500 dark:border-primary-400 ring-2 ring-primary-500/20 dark:ring-primary-400/20' : ''}
          ${disabled ? 'cursor-not-allowed' : ''}
          ${className || ""}
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        id={id}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={rest["aria-label"]}
      >
        {leftIcon && <View className="flex-shrink-0 mr-2 text-slate-500 dark:text-slate-400">{leftIcon}</View>}
        <View className="flex-1 text-left">
          {selectedValue ? (
            <Text as="span" className="text-slate-900 dark:text-slate-100">{selectedLabel}</Text>
          ) : (
            <Text as="span" className="text-slate-500 dark:text-slate-400">{placeholder}</Text>
          )}
        </View>
        {rightIcon ? (
          <View className="flex-shrink-0 ml-2 text-slate-500 dark:text-slate-400">{rightIcon}</View>
        ) : (
          <View className={`flex-shrink-0 ml-2 transition-transform duration-200 text-slate-500 dark:text-slate-400 ${isOpen ? "rotate-180" : ""}`}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </View>
        )}
      </View>

      {isOpen && (
        <ul className="
          absolute top-full left-0 right-0 mt-1 py-1 z-50
          bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700
          rounded-lg shadow-lg max-h-60 overflow-auto
        " role="listbox">
          {options.map((option) => (
            <li
              key={option.value}
              className={`
                px-3 py-2 text-sm cursor-pointer transition-colors duration-150
                ${selectedValue === option.value
                  ? "bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400"
                  : "text-slate-900 dark:text-slate-100 hover:bg-slate-50 dark:hover:bg-slate-700"
                }
                ${option.disabled ? "opacity-50 cursor-not-allowed" : ""}
              `}
              onClick={() => !option.disabled && handleOptionClick(option.value)}
              role="option"
              aria-selected={selectedValue === option.value}
            >
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </View>
  );
};

export default Dropdown;