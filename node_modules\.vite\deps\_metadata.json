{"hash": "51eb2522", "configHash": "a762cc2d", "lockfileHash": "193bcb33", "browserHash": "52c2f6b2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d530fd9c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "4beec61c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c03c728d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b7c3d499", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "03bd091e", "needsInterop": false}, "@tiptap/extension-text-align": {"src": "../../@tiptap/extension-text-align/dist/index.js", "file": "@tiptap_extension-text-align.js", "fileHash": "007871d6", "needsInterop": false}, "@tiptap/react": {"src": "../../@tiptap/react/dist/index.js", "file": "@tiptap_react.js", "fileHash": "b8edd11b", "needsInterop": false}, "@tiptap/starter-kit": {"src": "../../@tiptap/starter-kit/dist/index.js", "file": "@tiptap_starter-kit.js", "fileHash": "703e8c22", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "939b90a7", "needsInterop": false}, "dayjs": {"src": "../../dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "25a65918", "needsInterop": true}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "d4d48ba2", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "c55db808", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/esm/index.js", "file": "react-day-picker.js", "fileHash": "6e493cd5", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "39dd4d87", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "ac456647", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "1e65a2a8", "needsInterop": false}, "yup": {"src": "../../yup/index.esm.js", "file": "yup.js", "fileHash": "af041c3d", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "f843224c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a6771f86", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "dbe6a280", "needsInterop": false}, "react-apexcharts": {"src": "../../react-apexcharts/dist/react-apexcharts.min.js", "file": "react-apexcharts.js", "fileHash": "0f2535a1", "needsInterop": true}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-XRPIAATO": {"file": "chunk-XRPIAATO.js"}, "chunk-D2P3IO6H": {"file": "chunk-D2P3IO6H.js"}, "chunk-76BQHZKB": {"file": "chunk-76BQHZKB.js"}, "chunk-EWTE5DHJ": {"file": "chunk-EWTE5DHJ.js"}}}