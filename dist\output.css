*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-right-1 {
  right: -0.25rem;
}

.-top-1 {
  top: -0.25rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-4 {
  bottom: 1rem;
}

.left-0 {
  left: 0px;
}

.left-1 {
  left: 0.25rem;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.right-0 {
  right: 0px;
}

.right-10 {
  right: 2.5rem;
}

.right-2 {
  right: 0.5rem;
}

.right-3 {
  right: 0.75rem;
}

.right-4 {
  right: 1rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: 0.5rem;
}

.top-4 {
  top: 1rem;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-my-1 {
  margin-top: -0.25rem;
  margin-bottom: -0.25rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.-mb-8 {
  margin-bottom: -2rem;
}

.-ml-4 {
  margin-left: -1rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.-mr-8 {
  margin-right: -2rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-10 {
  margin-left: 2.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.h-0 {
  height: 0px;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-24 {
  height: 6rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[1px\] {
  height: 1px;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-80 {
  max-height: 20rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[60vh\] {
  max-height: 60vh;
}

.max-h-\[95vh\] {
  max-height: 95vh;
}

.max-h-screen {
  max-height: 100vh;
}

.min-h-11 {
  min-height: 2.75rem;
}

.min-h-12 {
  min-height: 3rem;
}

.min-h-64 {
  min-height: 16rem;
}

.min-h-9 {
  min-height: 2.25rem;
}

.min-h-\[1\.5rem\] {
  min-height: 1.5rem;
}

.min-h-\[100px\] {
  min-height: 100px;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[32px\] {
  min-height: 32px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[40px\] {
  min-height: 40px;
}

.min-h-\[48px\] {
  min-height: 48px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-1 {
  width: 0.25rem;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-52 {
  width: 13rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[1px\] {
  width: 1px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[150px\] {
  min-width: 150px;
}

.min-w-\[20px\] {
  min-width: 20px;
}

.min-w-\[32px\] {
  min-width: 32px;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-32 {
  max-width: 8rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-\[90vw\] {
  max-width: 90vw;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.caption-bottom {
  caption-side: bottom;
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-9 {
  --tw-translate-x: 2.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.resize-y {
  resize: vertical;
}

.resize {
  resize: both;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.\!items-center {
  align-items: center !important;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.rounded {
  border-radius: 0.375rem;
}

.rounded-2xl {
  border-radius: 1.5rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.75rem;
}

.rounded-md {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 1rem;
}

.rounded-b-none {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.rounded-t-lg {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-r-2 {
  border-right-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.\!border-red-500 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1)) !important;
}

.border-accent-10 {
  border-color: var(--accent-10);
}

.border-accent-100 {
  border-color: var(--accent-100);
}

.border-accent-20 {
  border-color: var(--accent-20);
}

.border-accent-200 {
  border-color: var(--accent-200);
}

.border-accent-30 {
  border-color: var(--accent-30);
}

.border-accent-300 {
  border-color: var(--accent-300);
}

.border-accent-40 {
  border-color: var(--accent-40);
}

.border-accent-400 {
  border-color: var(--accent-400);
}

.border-accent-5 {
  border-color: var(--accent-5);
}

.border-accent-50 {
  border-color: var(--accent-50);
}

.border-accent-500 {
  border-color: var(--accent-500);
}

.border-accent-600 {
  border-color: var(--accent-600);
}

.border-accent-700 {
  border-color: var(--accent-700);
}

.border-accent-800 {
  border-color: var(--accent-800);
}

.border-accent-900 {
  border-color: var(--accent-900);
}

.border-accent-background {
  border-color: var(--tertiary-bg);
}

.border-accent-forg {
  border-color: var(--accent-foreground);
}

.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-border {
  border-color: var(--border);
}

.border-danger {
  --tw-border-opacity: 1;
  border-color: rgb(234 67 53 / var(--tw-border-opacity, 1));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-input {
  border-color: var(--input);
}

.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(232 234 237 / var(--tw-border-opacity, 1));
}

.border-neutral-300 {
  --tw-border-opacity: 1;
  border-color: rgb(218 220 224 / var(--tw-border-opacity, 1));
}

.border-primary {
  border-color: var(--primary);
}

.border-primary-10 {
  border-color: var(--primary-10);
}

.border-primary-100 {
  border-color: var(--primary-100);
}

.border-primary-20 {
  border-color: var(--primary-20);
}

.border-primary-200 {
  border-color: var(--primary-200);
}

.border-primary-30 {
  border-color: var(--primary-30);
}

.border-primary-300 {
  border-color: var(--primary-300);
}

.border-primary-40 {
  border-color: var(--primary-40);
}

.border-primary-400 {
  border-color: var(--primary-400);
}

.border-primary-5 {
  border-color: var(--primary-5);
}

.border-primary-50 {
  border-color: var(--primary-50);
}

.border-primary-500 {
  border-color: var(--primary-500);
}

.border-primary-600 {
  border-color: var(--primary-600);
}

.border-primary-700 {
  border-color: var(--primary-700);
}

.border-primary-800 {
  border-color: var(--primary-800);
}

.border-primary-900 {
  border-color: var(--primary-900);
}

.border-primary-background {
  border-color: var(--primary-bg);
}

.border-primary-forg {
  border-color: var(--primary-foreground);
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.border-red-800 {
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}

.border-secondary-10 {
  border-color: var(--secondary-10);
}

.border-secondary-100 {
  border-color: var(--secondary-100);
}

.border-secondary-20 {
  border-color: var(--secondary-20);
}

.border-secondary-200 {
  border-color: var(--secondary-200);
}

.border-secondary-30 {
  border-color: var(--secondary-30);
}

.border-secondary-300 {
  border-color: var(--secondary-300);
}

.border-secondary-40 {
  border-color: var(--secondary-40);
}

.border-secondary-400 {
  border-color: var(--secondary-400);
}

.border-secondary-5 {
  border-color: var(--secondary-5);
}

.border-secondary-50 {
  border-color: var(--secondary-50);
}

.border-secondary-500 {
  border-color: var(--secondary-500);
}

.border-secondary-600 {
  border-color: var(--secondary-600);
}

.border-secondary-700 {
  border-color: var(--secondary-700);
}

.border-secondary-800 {
  border-color: var(--secondary-800);
}

.border-secondary-900 {
  border-color: var(--secondary-900);
}

.border-secondary-background {
  border-color: var(--secondary-bg);
}

.border-secondary-forg {
  border-color: var(--secondary-foreground);
}

.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}

.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}

.border-slate-200\/50 {
  border-color: rgb(226 232 240 / 0.5);
}

.border-slate-300 {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}

.border-success {
  --tw-border-opacity: 1;
  border-color: rgb(54 179 126 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: transparent;
}

.\!bg-\[\#1D4ED8\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-background {
  background-color: var(--background) !important;
}

.\!bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2) !important;
}

.\!bg-card {
  background-color: var(--card) !important;
}

.\!bg-emerald-500\/20 {
  background-color: rgb(16 185 129 / 0.2) !important;
}

.\!bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2) !important;
}

.\!bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2) !important;
}

.\!bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2) !important;
}

.\!bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2) !important;
}

.\!bg-transparent {
  background-color: transparent !important;
}

.bg-\[var\(--primary-foreground\)\] {
  background-color: var(--primary-foreground);
}

.bg-accent-10 {
  background-color: var(--accent-10);
}

.bg-accent-100 {
  background-color: var(--accent-100);
}

.bg-accent-20 {
  background-color: var(--accent-20);
}

.bg-accent-200 {
  background-color: var(--accent-200);
}

.bg-accent-30 {
  background-color: var(--accent-30);
}

.bg-accent-300 {
  background-color: var(--accent-300);
}

.bg-accent-40 {
  background-color: var(--accent-40);
}

.bg-accent-400 {
  background-color: var(--accent-400);
}

.bg-accent-5 {
  background-color: var(--accent-5);
}

.bg-accent-50 {
  background-color: var(--accent-50);
}

.bg-accent-500 {
  background-color: var(--accent-500);
}

.bg-accent-600 {
  background-color: var(--accent-600);
}

.bg-accent-700 {
  background-color: var(--accent-700);
}

.bg-accent-800 {
  background-color: var(--accent-800);
}

.bg-accent-900 {
  background-color: var(--accent-900);
}

.bg-accent-background {
  background-color: var(--tertiary-bg);
}

.bg-accent-forg {
  background-color: var(--accent-foreground);
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-background {
  background-color: var(--background);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-border {
  background-color: var(--border);
}

.bg-card {
  background-color: var(--card);
}

.bg-danger\/10 {
  background-color: rgb(234 67 53 / 0.1);
}

.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.bg-muted {
  background-color: var(--muted);
}

.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));
}

.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(232 234 237 / var(--tw-bg-opacity, 1));
}

.bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(218 220 224 / var(--tw-bg-opacity, 1));
}

.bg-neutral-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(189 193 198 / var(--tw-bg-opacity, 1));
}

.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-orange-500\/10 {
  background-color: rgb(249 115 22 / 0.1);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-primary-10 {
  background-color: var(--primary-10);
}

.bg-primary-100 {
  background-color: var(--primary-100);
}

.bg-primary-20 {
  background-color: var(--primary-20);
}

.bg-primary-200 {
  background-color: var(--primary-200);
}

.bg-primary-30 {
  background-color: var(--primary-30);
}

.bg-primary-300 {
  background-color: var(--primary-300);
}

.bg-primary-40 {
  background-color: var(--primary-40);
}

.bg-primary-400 {
  background-color: var(--primary-400);
}

.bg-primary-5 {
  background-color: var(--primary-5);
}

.bg-primary-50 {
  background-color: var(--primary-50);
}

.bg-primary-500 {
  background-color: var(--primary-500);
}

.bg-primary-600 {
  background-color: var(--primary-600);
}

.bg-primary-700 {
  background-color: var(--primary-700);
}

.bg-primary-800 {
  background-color: var(--primary-800);
}

.bg-primary-900 {
  background-color: var(--primary-900);
}

.bg-primary-background {
  background-color: var(--primary-bg);
}

.bg-primary-forg {
  background-color: var(--primary-foreground);
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.bg-secondary {
  background-color: var(--secondary);
}

.bg-secondary-10 {
  background-color: var(--secondary-10);
}

.bg-secondary-100 {
  background-color: var(--secondary-100);
}

.bg-secondary-20 {
  background-color: var(--secondary-20);
}

.bg-secondary-200 {
  background-color: var(--secondary-200);
}

.bg-secondary-30 {
  background-color: var(--secondary-30);
}

.bg-secondary-300 {
  background-color: var(--secondary-300);
}

.bg-secondary-40 {
  background-color: var(--secondary-40);
}

.bg-secondary-400 {
  background-color: var(--secondary-400);
}

.bg-secondary-5 {
  background-color: var(--secondary-5);
}

.bg-secondary-50 {
  background-color: var(--secondary-50);
}

.bg-secondary-500 {
  background-color: var(--secondary-500);
}

.bg-secondary-600 {
  background-color: var(--secondary-600);
}

.bg-secondary-700 {
  background-color: var(--secondary-700);
}

.bg-secondary-800 {
  background-color: var(--secondary-800);
}

.bg-secondary-900 {
  background-color: var(--secondary-900);
}

.bg-secondary-background {
  background-color: var(--secondary-bg);
}

.bg-secondary-forg {
  background-color: var(--secondary-foreground);
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.bg-slate-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.bg-slate-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}

.bg-success {
  --tw-bg-opacity: 1;
  background-color: rgb(54 179 126 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/15 {
  background-color: rgb(255 255 255 / 0.15);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-emerald-500\/10 {
  background-color: rgb(16 185 129 / 0.1);
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}

.\!bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.\!from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-emerald-100 {
  --tw-gradient-from: #d1fae5 var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(209 250 229 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-green-100 {
  --tw-gradient-from: #dcfce7 var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(220 252 231 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-indigo-100 {
  --tw-gradient-from: #e0e7ff var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(224 231 255 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-orange-100 {
  --tw-gradient-from: #ffedd5 var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(255 237 213 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-purple-100 {
  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.\!from-red-100 {
  --tw-gradient-from: #fee2e2 var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(254 226 226 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-card {
  --tw-gradient-from: var(--card) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-900 {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-100 {
  --tw-gradient-from: var(--primary-100) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-50 {
  --tw-gradient-from: var(--primary-50) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-500 {
  --tw-gradient-from: var(--primary-500) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-600 {
  --tw-gradient-from: var(--primary-600) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-100 {
  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.\!via-blue-200 {
  --tw-gradient-to: rgb(191 219 254 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #bfdbfe var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-emerald-200 {
  --tw-gradient-to: rgb(167 243 208 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #a7f3d0 var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-green-200 {
  --tw-gradient-to: rgb(187 247 208 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #bbf7d0 var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-indigo-200 {
  --tw-gradient-to: rgb(199 210 254 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #c7d2fe var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-orange-200 {
  --tw-gradient-to: rgb(254 215 170 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #fed7aa var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-purple-200 {
  --tw-gradient-to: rgb(233 213 255 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #e9d5ff var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.\!via-red-200 {
  --tw-gradient-to: rgb(254 202 202 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), #fecaca var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.via-primary-200 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--primary-200) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary-600 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--primary-600) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-600 {
  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.\!to-blue-300 {
  --tw-gradient-to: #93c5fd var(--tw-gradient-to-position) !important;
}

.\!to-emerald-300 {
  --tw-gradient-to: #6ee7b7 var(--tw-gradient-to-position) !important;
}

.\!to-green-300 {
  --tw-gradient-to: #86efac var(--tw-gradient-to-position) !important;
}

.\!to-indigo-300 {
  --tw-gradient-to: #a5b4fc var(--tw-gradient-to-position) !important;
}

.\!to-orange-300 {
  --tw-gradient-to: #fdba74 var(--tw-gradient-to-position) !important;
}

.\!to-purple-300 {
  --tw-gradient-to: #d8b4fe var(--tw-gradient-to-position) !important;
}

.\!to-red-300 {
  --tw-gradient-to: #fca5a5 var(--tw-gradient-to-position) !important;
}

.to-black {
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}

.to-primary-100 {
  --tw-gradient-to: var(--primary-100) var(--tw-gradient-to-position);
}

.to-primary-300 {
  --tw-gradient-to: var(--primary-300) var(--tw-gradient-to-position);
}

.to-primary-600 {
  --tw-gradient-to: var(--primary-600) var(--tw-gradient-to-position);
}

.to-primary-700 {
  --tw-gradient-to: var(--primary-700) var(--tw-gradient-to-position);
}

.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}

.to-emerald-100 {
  --tw-gradient-to: #d1fae5 var(--tw-gradient-to-position);
}

.stroke-\[1\.5\] {
  stroke-width: 1.5;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0 {
  padding: 0px;
}

.p-0\.5 {
  padding: 0.125rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-2\.5 {
  padding-left: 0.625rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.\!text-2xl {
  font-size: 1.5rem !important;
  line-height: 2rem !important;
}

.\!text-3xl {
  font-size: 1.875rem !important;
  line-height: 2.25rem !important;
}

.\!text-xl {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extralight {
  font-weight: 200;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.font-thin {
  font-weight: 100;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.\!text-blue-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1)) !important;
}

.\!text-blue-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1)) !important;
}

.\!text-emerald-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1)) !important;
}

.\!text-emerald-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1)) !important;
}

.\!text-green-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1)) !important;
}

.\!text-green-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1)) !important;
}

.\!text-indigo-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1)) !important;
}

.\!text-orange-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1)) !important;
}

.\!text-orange-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1)) !important;
}

.\!text-primary {
  color: var(--primary) !important;
}

.\!text-purple-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1)) !important;
}

.\!text-purple-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1)) !important;
}

.\!text-red-400 {
  --tw-text-opacity: 1 !important;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1)) !important;
}

.\!text-red-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1)) !important;
}

.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.text-\[var\(--primary\)\] {
  color: var(--primary);
}

.text-accent {
  color: var(--accent);
}

.text-accent-10 {
  color: var(--accent-10);
}

.text-accent-100 {
  color: var(--accent-100);
}

.text-accent-20 {
  color: var(--accent-20);
}

.text-accent-200 {
  color: var(--accent-200);
}

.text-accent-30 {
  color: var(--accent-30);
}

.text-accent-300 {
  color: var(--accent-300);
}

.text-accent-40 {
  color: var(--accent-40);
}

.text-accent-400 {
  color: var(--accent-400);
}

.text-accent-5 {
  color: var(--accent-5);
}

.text-accent-50 {
  color: var(--accent-50);
}

.text-accent-500 {
  color: var(--accent-500);
}

.text-accent-600 {
  color: var(--accent-600);
}

.text-accent-700 {
  color: var(--accent-700);
}

.text-accent-800 {
  color: var(--accent-800);
}

.text-accent-900 {
  color: var(--accent-900);
}

.text-accent-background {
  color: var(--tertiary-bg);
}

.text-accent-forg {
  color: var(--accent-foreground);
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-amber-600\/80 {
  color: rgb(217 119 6 / 0.8);
}

.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-50 {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-600\/80 {
  color: rgb(37 99 235 / 0.8);
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-card-foreground {
  color: var(--card-foreground);
}

.text-current {
  color: currentColor;
}

.text-danger {
  --tw-text-opacity: 1;
  color: rgb(234 67 53 / var(--tw-text-opacity, 1));
}

.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}

.text-foreground {
  color: var(--foreground);
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-50 {
  --tw-text-opacity: 1;
  color: rgb(240 253 244 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.text-muted {
  color: var(--muted);
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

.text-neutral-100 {
  --tw-text-opacity: 1;
  color: rgb(248 249 250 / var(--tw-text-opacity, 1));
}

.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(189 193 198 / var(--tw-text-opacity, 1));
}

.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(154 160 166 / var(--tw-text-opacity, 1));
}

.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(128 134 139 / var(--tw-text-opacity, 1));
}

.text-neutral-700 {
  --tw-text-opacity: 1;
  color: rgb(95 99 104 / var(--tw-text-opacity, 1));
}

.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(32 33 36 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-primary {
  color: var(--primary);
}

.text-primary-10 {
  color: var(--primary-10);
}

.text-primary-100 {
  color: var(--primary-100);
}

.text-primary-20 {
  color: var(--primary-20);
}

.text-primary-200 {
  color: var(--primary-200);
}

.text-primary-30 {
  color: var(--primary-30);
}

.text-primary-300 {
  color: var(--primary-300);
}

.text-primary-40 {
  color: var(--primary-40);
}

.text-primary-400 {
  color: var(--primary-400);
}

.text-primary-5 {
  color: var(--primary-5);
}

.text-primary-50 {
  color: var(--primary-50);
}

.text-primary-500 {
  color: var(--primary-500);
}

.text-primary-600 {
  color: var(--primary-600);
}

.text-primary-700 {
  color: var(--primary-700);
}

.text-primary-800 {
  color: var(--primary-800);
}

.text-primary-900 {
  color: var(--primary-900);
}

.text-primary-background {
  color: var(--primary-bg);
}

.text-primary-forg {
  color: var(--primary-foreground);
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-600\/80 {
  color: rgb(220 38 38 / 0.8);
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-secondary {
  color: var(--secondary);
}

.text-secondary-10 {
  color: var(--secondary-10);
}

.text-secondary-100 {
  color: var(--secondary-100);
}

.text-secondary-20 {
  color: var(--secondary-20);
}

.text-secondary-200 {
  color: var(--secondary-200);
}

.text-secondary-30 {
  color: var(--secondary-30);
}

.text-secondary-300 {
  color: var(--secondary-300);
}

.text-secondary-40 {
  color: var(--secondary-40);
}

.text-secondary-400 {
  color: var(--secondary-400);
}

.text-secondary-5 {
  color: var(--secondary-5);
}

.text-secondary-50 {
  color: var(--secondary-50);
}

.text-secondary-500 {
  color: var(--secondary-500);
}

.text-secondary-600 {
  color: var(--secondary-600);
}

.text-secondary-700 {
  color: var(--secondary-700);
}

.text-secondary-800 {
  color: var(--secondary-800);
}

.text-secondary-900 {
  color: var(--secondary-900);
}

.text-secondary-background {
  color: var(--secondary-bg);
}

.text-secondary-forg {
  color: var(--secondary-foreground);
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.text-success {
  --tw-text-opacity: 1;
  color: rgb(54 179 126 / var(--tw-text-opacity, 1));
}

.text-text-light {
  --tw-text-opacity: 1;
  color: rgb(95 99 104 / var(--tw-text-opacity, 1));
}

.text-text-lighter {
  --tw-text-opacity: 1;
  color: rgb(154 160 166 / var(--tw-text-opacity, 1));
}

.text-warning {
  --tw-text-opacity: 1;
  color: rgb(251 188 5 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}

.text-yellow-50 {
  --tw-text-opacity: 1;
  color: rgb(254 252 232 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.placeholder-slate-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}

.placeholder-slate-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}

.placeholder-slate-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}

.placeholder-slate-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-10 {
  opacity: 0.1;
}

.opacity-100 {
  opacity: 1;
}

.opacity-15 {
  opacity: 0.15;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-90 {
  opacity: 0.9;
}

.\!shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-card {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-dropdown {
  --tw-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 2px 5px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-medium {
  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-soft {
  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\!shadow-blue-500\/25 {
  --tw-shadow-color: rgb(59 130 246 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-emerald-500\/25 {
  --tw-shadow-color: rgb(16 185 129 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-green-500\/25 {
  --tw-shadow-color: rgb(34 197 94 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-indigo-500\/25 {
  --tw-shadow-color: rgb(99 102 241 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-orange-500\/25 {
  --tw-shadow-color: rgb(249 115 22 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-purple-500\/25 {
  --tw-shadow-color: rgb(168 85 247 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.\!shadow-red-500\/25 {
  --tw-shadow-color: rgb(239 68 68 / 0.25) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.shadow-blue-50 {
  --tw-shadow-color: #eff6ff;
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-card {
  --tw-shadow-color: var(--card);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-red-100 {
  --tw-shadow-color: #fee2e2;
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-primary {
  --tw-ring-color: var(--primary);
}

.ring-primary-500 {
  --tw-ring-color: var(--primary-500);
}

.ring-red-500\/20 {
  --tw-ring-color: rgb(239 68 68 / 0.2);
}

.ring-slate-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(226 232 240 / var(--tw-ring-opacity, 1));
}

.ring-opacity-50 {
  --tw-ring-opacity: 0.5;
}

.ring-offset-background {
  --tw-ring-offset-color: var(--background);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

html {
  font-size: 80%;
  /* Compact sizing to match 90% zoom appearance */
}

body, #root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-family: "Inter", "Work Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* * {
    scrollbar-width: thin;
    scrollbar-color: var(--primary) #1a1a1a;
  }

  *::-webkit-scrollbar {
    width: 8px;
  }

  *::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 2px;
  }

  *::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 5px;
    border: 2px solid #1a1a1a;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: var(--primary-600);
    box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
  } */

/* Modern Scrollbar Styles */

/* Default (Light Mode) */

* {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.4) transparent;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
  border-radius: 4px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  border: none;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

*::-webkit-scrollbar-thumb:active {
  background: rgba(148, 163, 184, 0.8);
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* Dark Mode */

html.dark * {
  scrollbar-color: rgba(71, 85, 105, 0.4) transparent;
}

html.dark *::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.4);
}

html.dark *::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.6);
  box-shadow: 0 2px 8px rgba(71, 85, 105, 0.2);
}

html.dark *::-webkit-scrollbar-thumb:active {
  background: rgba(71, 85, 105, 0.8);
}

/* Thin scrollbar variant for dropdowns and small containers */

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

html.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

html.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* Hide scrollbar variant */

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Modern Medical Admin Theme */

:root {
  /* Base colors - Clean medical whites and professional grays */
  --background: #f8fafc;
  --foreground: #1e293b;
  /* UI Component backgrounds */
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  /* Updated color palette for modern medical admin */
  --primary-base: #3b82f6;
  --secondary-base: #10b981;
  --accent-base: #f59e0b;
  /* Primary colors - Calming blue */
  --primary: var(--primary-base);
  --primary-foreground: #ffffff;
  /* Primary color variants - Modern blue palette */
  --primary-10: var(--primary-10-base, #eff6ff);
  --primary-20: var(--primary-20-base, #dbeafe);
  --primary-30: var(--primary-30-base, #bfdbfe);
  --primary-40: var(--primary-40-base, #93c5fd);
  --primary-50: var(--primary-50-base, #60a5fa);
  --primary-100: var(--primary-100-base, #3b82f6);
  --primary-200: var(--primary-200-base, #2563eb);
  --primary-300: var(--primary-300-base, #1d4ed8);
  --primary-400: var(--primary-400-base, #1e40af);
  --primary-500: var(--primary-500-base, #1e3a8a);
  --primary-600: var(--primary-600-base, #1e40af);
  --primary-700: var(--primary-700-base, #1d4ed8);
  --primary-800: var(--primary-800-base, #2563eb);
  --primary-900: var(--primary-900-base, #1e40af);
  /* Secondary colors - Healing teal */
  --secondary: var(--secondary-base);
  --secondary-foreground: #ffffff;
  /* Secondary color variants - Modern emerald/green palette */
  --primary-bg: var(--primary-background-base, #eff6ff);
  --secondary-10:   var(--secondary-10-base,   #ecfdf5);
  --secondary-20:   var(--secondary-20-base,   #d1fae5);
  --secondary-30:   var(--secondary-30-base,   #a7f3d0);
  --secondary-40:   var(--secondary-40-base,   #6ee7b7);
  --secondary-50:   var(--secondary-50-base,   #34d399);
  --secondary-100:  var(--secondary-100-base,  #10b981);
  --secondary-200:  var(--secondary-200-base,  #059669);
  --secondary-300:  var(--secondary-300-base,  #047857);
  --secondary-400:  var(--secondary-400-base,  #065f46);
  --secondary-500:  var(--secondary-500-base,  #064e3b);
  --secondary-600:  var(--secondary-600-base,  #047857);
  --secondary-700:  var(--secondary-700-base,  #059669);
  --secondary-800:  var(--secondary-800-base,  #10b981);
  --secondary-900:  var(--secondary-900-base,  #34d399);
  /* Accent - Soft purple for highlighting important elements */
  --accent: var(--accent-base);
  --accent-foreground: #ffffff;
  /* Accent color variants - Modern amber/orange palette */
  --accent-10:   var(--accent-10-base,   #fffbeb);
  --accent-20:   var(--accent-20-base,   #fef3c7);
  --accent-30:   var(--accent-30-base,   #fde68a);
  --accent-40:   var(--accent-40-base,   #fcd34d);
  --accent-50:   var(--accent-50-base,   #fbbf24);
  --accent-100:  var(--accent-100-base,  #f59e0b);
  --accent-200:  var(--accent-200-base,  #d97706);
  --accent-300:  var(--accent-300-base,  #b45309);
  --accent-400:  var(--accent-400-base,  #92400e);
  --accent-500:  var(--accent-500-base,  #78350f);
  --accent-600:  var(--accent-600-base,  #92400e);
  --accent-700:  var(--accent-700-base,  #b45309);
  --accent-800:  var(--accent-800-base,  #d97706);
  --accent-900:  var(--accent-900-base,  #f59e0b);
  /* Status colors - Modern medical status palette */
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  /* Neutral/UI colors - Clean modern grays */
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #f8fafc;
  --ring: #3b82f6;
  /* Radius for consistent component styling */
  --radius: 0.5rem;
}

html.dark {
  /* Base colors - Modern dark theme for medical admin */
  --background: #0f172a;
  --foreground: #f1f5f9;
  /* UI Component backgrounds - Clean dark cards */
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  /* Primary colors - Bright blue for dark mode visibility */
  --primary: var(--primary-base);
  --primary-foreground: #0f172a;
  /* Primary color variants for dark mode */
  --primary-10: var(--primary-10-base, #F8FAFD);
  --primary-20: var(--primary-20-base, #F2F5FA);
  --primary-30: var(--primary-30-base, #EAF0F7);
  --primary-40: var(--primary-40-base, #D9E4F0);
  --primary-50: var(--primary-50-base, #F5F9FF);
  --primary-100: var(--primary-100-base, #E8F1FE);
  --primary-200: var(--primary-200-base, #C9DFFC);
  --primary-300: var(--primary-300-base, #92BFFA);
  --primary-400: var(--primary-400-base, #5A9DF7);
  --primary-500: var(--primary-500-base, #1A73E8);
  --primary-600: var(--primary-600-base, #1259B8);
  --primary-700: var(--primary-700-base, #0D4287);
  --primary-800: var(--primary-800-base, #072C5E);
  --primary-900: var(--primary-900-base, #04172F);
  /* Secondary colors - Bright emerald for dark mode */
  --secondary: var(--secondary-base);
  --secondary-foreground: #0f172a;
  /* Secondary color variants for dark mode */
  --secondary-10:   var(--primary-10-base,   #EBF9F3);
  --secondary-20:   var(--primary-20-base,   #D7F4E7);
  --secondary-30:   var(--primary-30-base,   #BFEEDC);
  --secondary-40:   var(--primary-40-base,   #A6E8D0);
  --secondary-50:   var(--primary-50-base,   #8EE2C4);
  --secondary-100:  var(--primary-100-base,  #75DCB8);
  --secondary-200:  var(--primary-200-base,  #5CD6AC);
  --secondary-300:  var(--primary-300-base,  #43D0A0);
  --secondary-400:  var(--primary-400-base,  #2ACB94);
  --secondary-500:  var(--primary-500-base,  #36B37E);
  --secondary-600:  var(--primary-600-base,  #2C9467);
  --secondary-700:  var(--primary-700-base,  #227551);
  --secondary-800:  var(--primary-800-base,  #18573A);
  --secondary-900:  var(--primary-900-base,  #0E3824);
  /* Accent - Bright amber for dark mode contrast */
  --accent: var(--accent-base);
  --accent-foreground: #0f172a;
  /* Accent color variants for dark mode */
  --tertiary-10:   var(--tertiary-10-base,   #FFF9E8);
  --tertiary-20:   var(--tertiary-20-base,   #FFF2D1);
  --tertiary-30:   var(--tertiary-30-base,   #FFE8AF);
  --tertiary-40:   var(--tertiary-40-base,   #FFDD8C);
  --tertiary-50:   var(--tertiary-50-base,   #FFD26A);
  --tertiary-100:  var(--tertiary-100-base,  #FFC747);
  --tertiary-200:  var(--tertiary-200-base,  #FFBC25);
  --tertiary-300:  var(--tertiary-300-base,  #FBBC05);
  --tertiary-400:  var(--tertiary-400-base,  #DB9F04);
  --tertiary-500:  var(--tertiary-500-base,  #BB8403);
  --tertiary-600:  var(--tertiary-600-base,  #9C6A02);
  --tertiary-700:  var(--tertiary-700-base,  #7C5202);
  --tertiary-800:  var(--tertiary-800-base,  #5C3B01);
  --tertiary-900:  var(--tertiary-900-base,  #3D2501);
  /* Status colors - bright colors for dark mode visibility */
  --success: #34d399;
  --success-foreground: #0f172a;
  --warning: #fbbf24;
  --warning-foreground: #0f172a;
  --destructive: #f87171;
  --destructive-foreground: #0f172a;
  /* Neutral/UI colors - Dark mode grays */
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --border: #475569;
  --input: #334155;
  --ring: #60a5fa;
}

/* Custom animations and utilities */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth transitions for all interactive elements */

* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar styling for webkit browsers */

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* Dark mode scrollbar */

html.dark ::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.5);
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.8);
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: var(--foreground);
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: var(--muted-foreground);
}

.placeholder\:text-muted-foreground::placeholder {
  color: var(--muted-foreground);
}

.last\:border-0:last-child {
  border-width: 0px;
}

.checked\:border-2:checked {
  border-width: 2px;
}

.checked\:border-primary:checked {
  border-color: var(--primary);
}

.checked\:border-primary-600:checked {
  border-color: var(--primary-600);
}

.checked\:bg-primary-600:checked {
  background-color: var(--primary-600);
}

.checked\:bg-white:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-primary-500:hover {
  border-color: var(--primary-500);
}

.hover\:border-slate-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}

.hover\:bg-black\/70:hover {
  background-color: rgb(0 0 0 / 0.7);
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-card:hover {
  background-color: var(--card);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted:hover {
  background-color: var(--muted);
}

.hover\:bg-neutral-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-100:hover {
  background-color: var(--primary-100);
}

.hover\:bg-primary-200:hover {
  background-color: var(--primary-200);
}

.hover\:bg-primary-600:hover {
  background-color: var(--primary-600);
}

.hover\:bg-primary-700:hover {
  background-color: var(--primary-700);
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500\/20:hover {
  background-color: rgb(239 68 68 / 0.2);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-80:hover {
  --tw-bg-opacity: 0.8;
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-primary-700:hover {
  --tw-gradient-from: var(--primary-700) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-purple-700:hover {
  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #7e22ce var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.hover\:to-pink-700:hover {
  --tw-gradient-to: #be185d var(--tw-gradient-to-position);
}

.hover\:to-primary-800:hover {
  --tw-gradient-to: var(--primary-800) var(--tw-gradient-to-position);
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-700:hover {
  --tw-text-opacity: 1;
  color: rgb(95 99 104 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  color: var(--primary);
}

.hover\:text-primary-500:hover {
  color: var(--primary-500);
}

.hover\:text-primary-600:hover {
  color: var(--primary-600);
}

.hover\:text-primary-700:hover {
  color: var(--primary-700);
}

.hover\:text-primary-800:hover {
  color: var(--primary-800);
}

.hover\:text-red-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-secondary:hover {
  color: var(--secondary);
}

.hover\:text-slate-600:hover {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-700:hover {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-900:hover {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-800:hover {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-medium:hover {
  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-soft:hover {
  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-primary:focus {
  border-color: var(--primary);
}

.focus\:border-primary-400:focus {
  border-color: var(--primary-400);
}

.focus\:border-primary-500:focus {
  border-color: var(--primary-500);
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:opacity-100:focus {
  opacity: 1;
}

.focus\:shadow-lg:focus {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-medium:focus {
  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-danger\/30:focus {
  --tw-ring-color: rgb(234 67 53 / 0.3);
}

.focus\:ring-primary:focus {
  --tw-ring-color: var(--primary);
}

.focus\:ring-primary-300:focus {
  --tw-ring-color: var(--primary-300);
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: var(--primary-500);
}

.focus\:ring-red-500\/20:focus {
  --tw-ring-color: rgb(239 68 68 / 0.2);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-primary:focus-visible {
  --tw-ring-color: var(--primary);
}

.focus-visible\:ring-primary-500:focus-visible {
  --tw-ring-color: var(--primary-500);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: var(--ring);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.group:hover .group-hover\:text-current {
  color: currentColor;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: var(--muted);
}

.dark\:border-b:is(.dark *) {
  border-bottom-width: 1px;
}

.dark\:border-none:is(.dark *) {
  border-style: none;
}

.dark\:border-background:is(.dark *) {
  border-color: var(--background);
}

.dark\:border-blue-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.dark\:border-border:is(.dark *) {
  border-color: var(--border);
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:border-green-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}

.dark\:border-primary-400:is(.dark *) {
  border-color: var(--primary-400);
}

.dark\:border-primary-600:is(.dark *) {
  border-color: var(--primary-600);
}

.dark\:border-primary-700:is(.dark *) {
  border-color: var(--primary-700);
}

.dark\:border-purple-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}

.dark\:border-red-400:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.dark\:border-red-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.dark\:border-red-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}

.dark\:border-slate-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}

.dark\:border-slate-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}

.dark\:border-slate-700\/50:is(.dark *) {
  border-color: rgb(51 65 85 / 0.5);
}

.dark\:border-slate-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}

.dark\:border-white:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.dark\:border-white\/20:is(.dark *) {
  border-color: rgb(255 255 255 / 0.2);
}

.dark\:\!bg-card:is(.dark *) {
  background-color: var(--card) !important;
}

.dark\:bg-\[\#54555F\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(84 85 95 / var(--tw-bg-opacity, 1));
}

.dark\:bg-amber-400:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}

.dark\:bg-amber-900\/30:is(.dark *) {
  background-color: rgb(120 53 15 / 0.3);
}

.dark\:bg-background:is(.dark *) {
  background-color: var(--background);
}

.dark\:bg-blue-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-blue-900\/30:is(.dark *) {
  background-color: rgb(30 58 138 / 0.3);
}

.dark\:bg-card:is(.dark *) {
  background-color: var(--card);
}

.dark\:bg-emerald-400:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(52 211 153 / var(--tw-bg-opacity, 1));
}

.dark\:bg-emerald-900\/30:is(.dark *) {
  background-color: rgb(6 78 59 / 0.3);
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900\/20:is(.dark *) {
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:bg-green-900\/30:is(.dark *) {
  background-color: rgb(20 83 45 / 0.3);
}

.dark\:bg-neutral-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(95 99 104 / var(--tw-bg-opacity, 1));
}

.dark\:bg-neutral-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(60 64 67 / var(--tw-bg-opacity, 1));
}

.dark\:bg-primary:is(.dark *) {
  background-color: var(--primary);
}

.dark\:bg-primary-400:is(.dark *) {
  background-color: var(--primary-400);
}

.dark\:bg-primary-500:is(.dark *) {
  background-color: var(--primary-500);
}

.dark\:bg-primary-800:is(.dark *) {
  background-color: var(--primary-800);
}

.dark\:bg-purple-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}

.dark\:bg-purple-900\/30:is(.dark *) {
  background-color: rgb(88 28 135 / 0.3);
}

.dark\:bg-red-400:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:bg-red-900\/30:is(.dark *) {
  background-color: rgb(127 29 29 / 0.3);
}

.dark\:bg-slate-400:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}

.dark\:bg-slate-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}

.dark\:bg-slate-700\/50:is(.dark *) {
  background-color: rgb(51 65 85 / 0.5);
}

.dark\:bg-slate-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}

.dark\:bg-slate-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.dark\:bg-slate-900\/95:is(.dark *) {
  background-color: rgb(15 23 42 / 0.95);
}

.dark\:bg-transparent:is(.dark *) {
  background-color: transparent;
}

.dark\:bg-white:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.dark\:\!from-blue-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(30 64 175 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(30 64 175 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-emerald-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(6 95 70 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(6 95 70 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-green-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(22 101 52 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(22 101 52 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-indigo-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(55 48 163 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(55 48 163 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-orange-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(154 52 18 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(154 52 18 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-purple-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(107 33 168 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:\!from-red-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(153 27 27 / 0.4) var(--tw-gradient-from-position) !important;
  --tw-gradient-to: rgb(153 27 27 / 0) var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
}

.dark\:from-blue-950\/20:is(.dark *) {
  --tw-gradient-from: rgb(23 37 84 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(23 37 84 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-green-950\/20:is(.dark *) {
  --tw-gradient-from: rgb(5 46 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 46 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-primary-600:is(.dark *) {
  --tw-gradient-from: var(--primary-600) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-slate-900:is(.dark *) {
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-purple-900\/40:is(.dark *) {
  --tw-gradient-from: rgb(88 28 135 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:\!via-blue-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(29 78 216 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(29 78 216 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-emerald-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(4 120 87 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(4 120 87 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-green-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(21 128 61 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(21 128 61 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-indigo-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(67 56 202 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(67 56 202 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-orange-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(194 65 12 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(194 65 12 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-purple-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(126 34 206 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:\!via-red-700\/40:is(.dark *) {
  --tw-gradient-to: rgb(185 28 28 / 0)  var(--tw-gradient-to-position) !important;
  --tw-gradient-stops: var(--tw-gradient-from), rgb(185 28 28 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to) !important;
}

.dark\:via-primary-700:is(.dark *) {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--primary-700) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:\!to-blue-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(37 99 235 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-emerald-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(5 150 105 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-green-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(22 163 74 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-indigo-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(79 70 229 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-orange-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(234 88 12 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-purple-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(147 51 234 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:\!to-red-600\/40:is(.dark *) {
  --tw-gradient-to: rgb(220 38 38 / 0.4) var(--tw-gradient-to-position) !important;
}

.dark\:to-emerald-950\/20:is(.dark *) {
  --tw-gradient-to: rgb(2 44 34 / 0.2) var(--tw-gradient-to-position);
}

.dark\:to-indigo-950\/20:is(.dark *) {
  --tw-gradient-to: rgb(30 27 75 / 0.2) var(--tw-gradient-to-position);
}

.dark\:to-primary-800:is(.dark *) {
  --tw-gradient-to: var(--primary-800) var(--tw-gradient-to-position);
}

.dark\:to-slate-800:is(.dark *) {
  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);
}

.dark\:to-emerald-900\/40:is(.dark *) {
  --tw-gradient-to: rgb(6 78 59 / 0.4) var(--tw-gradient-to-position);
}

.dark\:\!text-blue-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-emerald-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-green-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-indigo-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-orange-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-purple-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1)) !important;
}

.dark\:\!text-red-400:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1)) !important;
}

.dark\:text-amber-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.dark\:text-amber-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.dark\:text-black:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-emerald-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-muted-foreground:is(.dark *) {
  color: var(--muted-foreground);
}

.dark\:text-neutral-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 249 250 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(218 220 224 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(189 193 198 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(154 160 166 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-600:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(128 134 139 / var(--tw-text-opacity, 1));
}

.dark\:text-primary:is(.dark *) {
  color: var(--primary);
}

.dark\:text-primary-300:is(.dark *) {
  color: var(--primary-300);
}

.dark\:text-primary-400:is(.dark *) {
  color: var(--primary-400);
}

.dark\:text-purple-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:placeholder-slate-400:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-slate-400:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-slate-500:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-slate-500:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}

.dark\:shadow-2xl:is(.dark *) {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:shadow-md:is(.dark *) {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:shadow-none:is(.dark *) {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:\!shadow-blue-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(96 165 250 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-emerald-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(52 211 153 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-green-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(74 222 128 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-indigo-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(129 140 248 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-orange-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(251 146 60 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-purple-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(192 132 252 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:\!shadow-red-400\/20:is(.dark *) {
  --tw-shadow-color: rgb(248 113 113 / 0.2) !important;
  --tw-shadow: var(--tw-shadow-colored) !important;
}

.dark\:ring-slate-700:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity, 1));
}

.dark\:checked\:border-primary-500:checked:is(.dark *) {
  border-color: var(--primary-500);
}

.dark\:checked\:bg-primary-500:checked:is(.dark *) {
  background-color: var(--primary-500);
}

.dark\:hover\:border-border:hover:is(.dark *) {
  border-color: var(--border);
}

.dark\:hover\:border-gray-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:hover\:border-primary:hover:is(.dark *) {
  border-color: var(--primary);
}

.dark\:hover\:border-primary-400:hover:is(.dark *) {
  border-color: var(--primary-400);
}

.dark\:hover\:border-slate-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-card:hover:is(.dark *) {
  background-color: var(--card);
}

.dark\:hover\:bg-green-900\/20:hover:is(.dark *) {
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:hover\:bg-primary:hover:is(.dark *) {
  background-color: var(--primary);
}

.dark\:hover\:bg-primary-800:hover:is(.dark *) {
  background-color: var(--primary-800);
}

.dark\:hover\:bg-red-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-red-900\/20:hover:is(.dark *) {
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:hover\:bg-slate-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-slate-700\/50:hover:is(.dark *) {
  background-color: rgb(51 65 85 / 0.5);
}

.dark\:hover\:bg-white\/10:hover:is(.dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-primary-200:hover:is(.dark *) {
  color: var(--primary-200);
}

.dark\:hover\:text-primary-300:hover:is(.dark *) {
  color: var(--primary-300);
}

.dark\:hover\:text-red-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-slate-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:focus\:border-primary-400:focus:is(.dark *) {
  border-color: var(--primary-400);
}

.dark\:focus\:ring-primary-400:focus:is(.dark *) {
  --tw-ring-color: var(--primary-400);
}

.dark\:focus\:ring-red-400\/20:focus:is(.dark *) {
  --tw-ring-color: rgb(248 113 113 / 0.2);
}

.dark\:focus\:ring-offset-slate-800:focus:is(.dark *) {
  --tw-ring-offset-color: #1e293b;
}

@media (min-width: 640px) {
  .sm\:bottom-0 {
    bottom: 0px;
  }

  .sm\:right-0 {
    right: 0px;
  }

  .sm\:top-auto {
    top: auto;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }
}

@media (min-width: 768px) {
  .md\:static {
    position: static;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:max-w-\[420px\] {
    max-width: 420px;
  }

  .md\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:block {
    display: block;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

@media print {
  .print\:hidden {
    display: none;
  }
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
