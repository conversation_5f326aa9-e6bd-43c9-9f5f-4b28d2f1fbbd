import Button from "@/components/button";
import Modal from "@/components/Modal";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import {
  EXAMINATION_DETAILS_URL,
  EXAMINATION_FORM_URL,
  EXAMINATION_TABLE_URL,
} from "@/utils/urls/frontend";
import { Plus, Stethoscope, Activity, Users, FileText } from "lucide-react";
import InfoCard from "@/components/ui/infoCard";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import PaginationComponent from "@/components/Pagination";
import ActionMenu from "@/components/editDeleteAction";
import { Link } from "react-router-dom";
import { handleSortChange } from "@/utils/helperFunctions";
import { useExaminations } from "@/actions/calls/examination";

export const ExaminationsPage = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const { examinationListHandler, deleteExaminationHandler, cleanUp } =
    useExaminations();

  const [deleteId, setDeleteId] = useState<null | string>(null);

  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      examinationListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const sortOptions: SortOption[] = [
    { label: "Patient Number (A-Z)", value: "patient_number", order: "asc" },
    { label: "Patient Number (Z-A)", value: "patient_number", order: "desc" },
    {
      label: "Temperature (Ascending)",
      value: "temperature",
      order: "asc",
    },
    {
      label: "Temperature (Descending)",
      value: "temperature",
      order: "desc",
    },

    {
      label: "BP (Ascending)",
      value: "bp",
      order: "asc",
    },
    {
      label: "BP (Descending)",
      value: "bp",
      order: "desc",
    },
    {
      label: "Pulse (Ascending)",
      value: "pulse",
      order: "asc",
    },
    {
      label: "Pulse (Descending)",
      value: "pulse",
      order: "desc",
    },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(null);
  const paginateObj = useSelector(
    (state: RootState) => state.examinations.userCompleteObj
  );


  return (
    <React.Fragment>
      <Modal
        title="Examination Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this Examination? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button variant="outline" onPress={modalCloseHandler}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onPress={() => {
              if (deleteId) {
                deleteExaminationHandler(deleteId, (_: boolean) => {
                  // if (success) {
                  examinationListHandler(
                    searchParams?.get("currentPage") ?? 1,
                    () => {
                      modalCloseHandler();
                    }
                  );
                  // }
                });
              }
            }}
          >
            Delete
          </Button>
        </View>
      </Modal>

      {/* Header Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex items-center gap-3">
          <View className="p-2 rounded-lg bg-primary/10">
            <Stethoscope className="h-6 w-6 text-primary" />
          </View>
          <View>
            <Text as="h1" className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1">
              Examinations
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
              Manage patient examinations and vital signs
            </Text>
          </View>
        </View>
      </View>

      {/* Stats Cards */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <InfoCard
          label="Total Examinations"
          value={paginateObj?.total || 0}
          valueStyle="!text-blue-600 dark:!text-blue-400"
          icon={<Stethoscope size={20} />}
          iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Today's Exams"
          value={paginateObj?.data?.filter((exam: any) => {
            const today = new Date().toISOString().split('T')[0];
            return exam.created_at?.split('T')[0] === today;
          }).length || 0}
          valueStyle="!text-emerald-600 dark:!text-emerald-400"
          icon={<Activity size={20} />}
          iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Patients Examined"
          value={new Set(paginateObj?.data?.map((exam: any) => exam.patient_id)).size || 0}
          valueStyle="!text-purple-600 dark:!text-purple-400"
          icon={<Users size={20} />}
          iconStyle="!bg-gradient-to-br !from-purple-100 !via-purple-200 !to-purple-300 dark:!from-purple-800/40 dark:!via-purple-700/40 dark:!to-purple-600/40 !text-purple-600 dark:!text-purple-400 !shadow-lg !shadow-purple-500/25 dark:!shadow-purple-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Avg Temperature"
          value={`${(paginateObj?.data?.reduce((sum: number, exam: any) => sum + (parseFloat(exam.temperature) || 0), 0) / (paginateObj?.data?.length || 1)).toFixed(1)}°F`}
          valueStyle="!text-orange-600 dark:!text-orange-400"
          icon={<FileText size={20} />}
          iconStyle="!bg-gradient-to-br !from-orange-100 !via-orange-200 !to-orange-300 dark:!from-orange-800/40 dark:!via-orange-700/40 dark:!to-orange-600/40 !text-orange-600 dark:!text-orange-400 !shadow-lg !shadow-orange-500/25 dark:!shadow-orange-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />
      </View>

      <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
        <View className="p-4 border-b border-neutral-200 bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center  dark:boder-border">
          <View className="flex gap-2 w-full  justify-between items-center ">
            <SearchBar
              onSearch={(value: string) => {
                setSearchParams({
                  ...Object.fromEntries([...searchParams]),
                  currentPage: "1",
                  search: value,
                });
              }}
              className="shadow-sm dark:shadow-none"
            />
            <View className="flex gap-3">
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(EXAMINATION_TABLE_URL + EXAMINATION_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Examination
              </Button>
            </View>
          </View>
        </View>
        {/* Table */}
        <DynamicTable
          tableHeaders={[
            "Patient Number",
            "Temperature",
            "BP",
            "Pulse",
            "Actions",
          ]}
          tableData={paginateObj?.data.map((examination: any) => [
            <Link
              to={`${EXAMINATION_TABLE_URL}${EXAMINATION_DETAILS_URL}/${examination.id}`}
            >
              {examination?.patient_number || "N/A"}
            </Link>,
            examination?.temperature || "N/A",
            examination?.bp || "N/A",
            examination?.pulse || "N/A",
            <ActionMenu
              onEdit={() =>
                navigate(
                  EXAMINATION_TABLE_URL +
                    EXAMINATION_FORM_URL +
                    "/" +
                    examination.id
                )
              }
              onDelete={() => {
                setDeleteId(examination.id);
              }}
            />,
          ])}
        />
        <PaginationComponent
          getPageNumberHandler={(page) => {
            setSearchParams({
              ...Object.fromEntries([...searchParams]),
              currentPage: `${page}`,
            });
          }}
          last_page={paginateObj?.last_page}
          current_page={paginateObj?.current_page}
        />
      </Card>
    </React.Fragment>
  );
};
