import Button from "@/components/button";
import Modal from "@/components/Modal";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import {
  ROLES_URL,
  EDIT_ROLE_URL,
  ROLES_TABLE_URL,
  // PERMISSIONS_URL,
} from "@/utils/urls/frontend";
import { Plus, Shield, Users, Settings, CheckCircle } from "lucide-react";
import InfoCard from "@/components/ui/infoCard";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useRoles } from "@/actions/calls/roles";
// import { RootState } from "@/actions/store";
import { useSelector } from "react-redux";
import ActionMenu from "@/components/editDeleteAction";
import PaginationComponent from "@/components/Pagination";
import { RootState } from "@/actions/store";
import { handleSortChange } from "@/utils/helperFunctions";
// import { get } from "node_modules/axios/index.d.cts";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import BouncingLoader from "@/components/BouncingLoader";
// import { Link } from "react-router-dom";

const RolesPage: React.FC<{}> = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const roles = useSelector((state: RootState) => state.roles.roles);
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const { getRoleList, deleteRole, cleanUp } = useRoles();
  const [description, setDescription] = useState<null | string>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      getRoleList(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
    searchParams?.get("currentPage"),
  ]);
  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const sortOptions: SortOption[] = [
    { label: "Roles (A-Z)", value: "name", order: "asc" },
    { label: "Roles (Z-A)", value: "name", order: "desc" },
    { label: "Description (A-Z)", value: "description", order: "asc" },
    { label: "Description (Z-A)", value: "description", order: "desc" },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  const handleDeleteRole = () => {
    if (deleteId) {
      deleteRole(deleteId, (success: boolean) => {
        if (success) {
          getRoleList(searchParams?.get("currentPage") ?? 1, () => {
            modalCloseHandler();
          });
        }
      });
    }
  };
  const modalDescriptionCloseHandler = () => {
    setDescription(null);
  };
  const handleDescription = (roleDescription: string) => {
    setDescription(roleDescription);
  };

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <Modal
        title="Role Description"
        isOpen={description !== null}
        onClose={modalDescriptionCloseHandler}
        description={description || ""}
      >
        <View className="flex justify-end gap-2">
          <Button
            className="text-black dark:text-white "
            variant="outline"
            onPress={modalDescriptionCloseHandler}
          >
            Cancel
          </Button>
        </View>
      </Modal>
      <Modal
        title="User Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this data? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button
            className="text-black"
            variant="outline"
            onPress={modalCloseHandler}
          >
            Cancel
          </Button>
          <Button variant="danger" onPress={handleDeleteRole}>
            Delete
          </Button>
        </View>
      </Modal>
      {/* Header Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex items-center gap-3">
          <View className="p-2 rounded-lg bg-primary/10">
            <Shield className="h-6 w-6 text-primary" />
          </View>
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1"
            >
              Roles & Permissions
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
              Manage user roles and access permissions
            </Text>
          </View>
        </View>
      </View>

      {/* Stats Cards */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <InfoCard
          label="Total Roles"
          value={roles?.total || 0}
          valueStyle="!text-blue-600 dark:!text-blue-400"
          icon={<Shield size={20} />}
          iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Active Roles"
          value={roles?.data?.filter((role: any) => role.status === 'active').length || 0}
          valueStyle="!text-emerald-600 dark:!text-emerald-400"
          icon={<CheckCircle size={20} />}
          iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Admin Roles"
          value={roles?.data?.filter((role: any) => role.name?.toLowerCase().includes('admin')).length || 0}
          valueStyle="!text-purple-600 dark:!text-purple-400"
          icon={<Settings size={20} />}
          iconStyle="!bg-gradient-to-br !from-purple-100 !via-purple-200 !to-purple-300 dark:!from-purple-800/40 dark:!via-purple-700/40 dark:!to-purple-600/40 !text-purple-600 dark:!text-purple-400 !shadow-lg !shadow-purple-500/25 dark:!shadow-purple-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="User Roles"
          value={roles?.data?.filter((role: any) => !role.name?.toLowerCase().includes('admin')).length || 0}
          valueStyle="!text-orange-600 dark:!text-orange-400"
          icon={<Users size={20} />}
          iconStyle="!bg-gradient-to-br !from-orange-100 !via-orange-200 !to-orange-300 dark:!from-orange-800/40 dark:!via-orange-700/40 dark:!to-orange-600/40 !text-orange-600 dark:!text-orange-400 !shadow-lg !shadow-orange-500/25 dark:!shadow-orange-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />
      </View>

      <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
        {/* Table */}
        <DynamicTable
          tableHeaders={["Roles", "Description", "Status", "Actions"]}
          // tableData= {roles}
          tableData={roles?.data?.map((role: any) => [
            // patient.patient_number,
            // <div>
            //   <Link to={ROLES_TABLE_URL + PERMISSIONS_URL}>{role.name}</Link>
            // </div>,
            role.name,
            <Button
              variant="ghost"
              onClick={() => handleDescription(role?.description)}
              className="text-left hover:text-primary underline"
            >
              {role?.description?.slice(0, 80) + "..." || "N/A"}
            </Button>,

            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(role?.status)}
            >
              {role.status || "N/A"}
            </Text>,
            <ActionMenu
              onEdit={
                role.name !== "Admin"
                  ? () =>
                      navigate(
                        ROLES_TABLE_URL + EDIT_ROLE_URL + "/" + role.id,
                        {
                          state: role,
                        }
                      )
                  : undefined
              }
              onDelete={
                role.name !== "Admin"
                  ? () => {
                      setDeleteId(role.id);
                    }
                  : undefined
              }
            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(val) =>
                  setSearchParams({
                    ...Object.fromEntries(searchParams),
                    search: val,
                    currentPage: "1",
                  })
                }
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                activeSort={activeSort ?? undefined}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
              />
            ),
            action: (
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(ROLES_TABLE_URL + ROLES_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Roles
              </Button>
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={roles?.current_page}
                last_page={roles?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};
export default RolesPage;
