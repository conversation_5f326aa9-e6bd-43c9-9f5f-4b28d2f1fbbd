name: Mark stale issues and pull requests
on:
  schedule:
  - cron: '21 14 * * *'
jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
    - uses: actions/stale@v9
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions.'
        stale-pr-message: 'This pull request has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions.'
        stale-issue-label: 'no-issue-activity'
        stale-pr-label: 'no-pr-activity'
        exempt-issue-labels: 'bug,high-priority'
        operations-per-run: 100
