import View from "@/components/view";
import Text from "@/components/text";
import Input from "@/components/input";
import Button from "@/components/button";
import React, { useEffect } from "react";
import { Camera, Edit } from "lucide-react";
import { useUsers } from "@/actions/calls/user";
import { toast } from "@/utils/custom-hooks/use-toast";
import { imageUpload } from "@/actions/calls/uesImage";
import { useDispatch, useSelector } from "react-redux";
import ImageComponent from "@/components/ui/ImageComponent";
import { Link, useNavigate, useParams } from "react-router-dom";
import { clearUserDetailsSlice } from "@/actions/slices/userSlice";
import { Table, TableRow, TableCell, TableBody } from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  DATE_FORMAT,
  EDIT_USER_URL,
  USER_TABLE_URL,
} from "@/utils/urls/frontend";
import ResetUserPassword from "@/components/resetUserPassword";
import dayjs from "dayjs";
import BouncingLoader from "@/components/BouncingLoader";

const UserDetailPage: React.FC<{}> = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const { role, id: userId } = localStorage.getItem("userDetails")
    ? JSON.parse(localStorage.getItem("userDetails") as string)
    : null;

  const { id } = useParams();
  const profileImageRef = React.useRef<HTMLInputElement>(null);

  const dispatch = useDispatch();

  const { getUserDetails, getProfilerDetails, cleanUp } = useUsers();

  const userDetails = useSelector((state: any) => state.users.userDetails);
  const [profileImagePreview, setProfileImagePreview] = React.useState<
    string | null
  >(null);
  const [isUploading, setIsUploading] = React.useState(false);

  useEffect(() => {
    if (id) {
      getUserDetails(
        id,
        () => {},
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    } else {
      getProfilerDetails(
        (_: boolean) => {},
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }

    // if(userDetails?.image){

    //   setProfileImagePreview(import.meta.env.VITE_APP_URL + userDetails?.image)
    // }
    return () => {
      cleanUp();
      dispatch(clearUserDetailsSlice());
    };
  }, [id]);

  useEffect(() => {
    if (userDetails?.image) {
      setProfileImagePreview(import.meta.env.VITE_APP_URL + userDetails.image);
    }
  }, [userDetails]);

  const handleProfileImageChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      uploadProfileImage(file);
    }
  };
  const uploadProfileImage = async (imageFile: File) => {
    if (!userDetails?.id) {
      toast({
        title: "Error!",
        description: "User ID not found",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    const imageUploadData = {
      id: userDetails.id,
      modal_type: "user",
      file_name: "image",
      folder_name: "users_image",
      image: imageFile,
    };

    imageUpload(imageUploadData, (success, _) => {
      setIsUploading(false);
      if (success) {
        toast({
          title: "Success!",
          description: "Profile image updated successfully",
          variant: "success",
        });

        // Refresh user details to get updated profile image
        if (id) {
          getUserDetails(id, () => {});
        } else {
          getProfilerDetails((_: boolean) => {});
        }
      } else {
        toast({
          title: "Error!",
          description: "Failed to upload image",
          variant: "destructive",
        });
      }
    });
  };

  const handleEditProfileClick = () => {
    navigate(USER_TABLE_URL + EDIT_USER_URL + "/" + userDetails?.id);
    // navigate(USER_PROFILE_URL + "/edit");
  };

  // Function to format date

  // Function to convert enum values to display format
  // const formatEnumValue = (value: string) => {
  //   return value.charAt(0) + value.slice(1).toLowerCase().replace(/_/g, " ");
  // };

  return (
    <View className="min-h-screen">
      <BouncingLoader isLoading={isLoading} />
      <View className="max-w-7xl mx-auto">
        <View className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-8">
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-3xl font-bold text-slate-900 dark:text-white mb-2"
            >
              User Details
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-lg">
              View detailed information about the user
            </Text>
          </View>
          <View className="flex gap-3">
            <Button variant="outline" className="px-4 py-2">
              <Link to={USER_TABLE_URL + "?currentPage=1"} className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Users
              </Link>
            </Button>
            <Button
              variant="primary"
              className="flex items-center justify-center gap-2 px-6 py-2 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl transition-all duration-200"
              onPress={handleEditProfileClick}
            >
              <Edit className="w-4 h-4" />
              Edit Profile
            </Button>
            {/* {id && (
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(USER_TABLE_URL + USER_URL);
                  dispatch(clearUserDetailsSlice());
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add User
              </Button>
            )} */}
          </View>
        </View>

        <View className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Profile Card */}
          <Card className="lg:col-span-1 border-0 shadow-medium bg-white dark:bg-slate-800">
            <CardHeader className="text-center ">
              <View className="flex justify-center mb-4 relative rounded-full ">
                {profileImagePreview ? (
                  <View className="w-32 h-32 rounded-full overflow-hidden relative border-4 border-border">
                    <ImageComponent
                      src={profileImagePreview}
                      alt={userDetails?.name || "User"}
                      className="rounded-full object-cover h-full"
                    />
                  </View>
                ) : (
                  <View className="w-32 h-32 rounded-full bg-secondary-50 flex items-center justify-center">
                    <Text
                      as="span"
                      weight="font-bold"
                      className="text-secondary-600 !text-xl"
                    >
                      {userDetails?.name?.charAt(0)}
                    </Text>
                  </View>
                )}

                {/* Upload button overlay */}
                <View
                  className="absolute"
                  style={{ bottom: "4px", right: "4px" }}
                >
                  <button
                    type="button"
                    className="flex items-center justify-center bg-primary hover:bg-purple-700 text-white rounded-full h-10 w-10 shadow-sm transition-all duration-200 border-2 border-border"
                    style={{ cursor: "pointer" }}
                    disabled={isUploading}
                    aria-label="Upload profile image"
                    onClick={() => profileImageRef.current?.click()}
                  >
                    <Camera className="h-5 w-5 cusor-pointer" />

                    {isUploading && (
                      <View className="absolute inset-0 flex items-center justify-center bg-transparent bg-transparent bg-opacity-80 rounded-ful cursor-pointer">
                        <View className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></View>
                      </View>
                    )}
                  </button>
                  <Input
                    type="file"
                    name="image"
                    ref={profileImageRef}
                    hidden
                    className="absolute inset-0 opacity-0 cursor-pointer rounded-full"
                    accept="image/*"
                    onChange={handleProfileImageChange}
                    disabled={isUploading}
                  />
                </View>
              </View>
              <CardTitle className="text-xl font-bold">
                {userDetails?.name || "N/A"}
              </CardTitle>
              {/* <span className="inline-block px-3 py-1 bg-primary-100 text-primary-600 rounded-full text-sm font-medium mt-2">
                {formatEnumValue(userDetails?.role)}
              </span> */}
            </CardHeader>
            <CardContent className="pt-0">
              <View className="space-y-3">
                <View className="flex items-center gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-primary"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  <Text
                    as="span"
                    className="text-text-light dark:text-gray-400"
                  >
                    <a href={`mailto:${userDetails?.email}`}>
                      {userDetails?.email || "N/A"}
                    </a>
                  </Text>
                </View>
                <View className="flex items-center gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-primary"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  <Text
                    as="span"
                    className="text-text-light dark:text-gray-400"
                  >
                    <a href={`tel:${userDetails?.phone}`}>
                      {userDetails?.phone || "N/A"}
                    </a>
                  </Text>
                </View>
                <View className="flex items-center gap-3">
                  <View>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-primary"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </View>
                  <Text
                    as="span"
                    className="text-text-light dark:text-gray-400"
                  >
                    {userDetails?.address}, {userDetails?.city},{" "}
                    {userDetails?.state}, {userDetails?.country}
                  </Text>
                </View>
                <View className="flex items-center gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-primary"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <Text
                    as="span"
                    className="text-text-light dark:text-gray-400"
                  >
                    {dayjs(userDetails?.dob).format(DATE_FORMAT)} (Age:{" "}
                    {userDetails?.age || "N/A"} years)
                  </Text>
                </View>
              </View>

              {/* {(!id || userDetails?.role === "Admin") && ( */}
              {(role === "Admin" || userDetails?.id === userId) && (
                <View className="mt-6 pt-6 border-t border-neutral-200">
                  {/* <View className="flex justify-between mb-4">
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2"
                   
                    onPress={handleEditProfileClick}
                  >
                    <Edit className="w-4 h-4" />
                    Edit Profile
                  </Button>
                </View> */}
                  <View className="flex justify-between mb-4">
                    <ResetUserPassword
                      role={role}
                      className="w-full"
                      userId={userDetails?.id}
                    />
                  </View>
                  {/* <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      userDetails?.status
                        ? "bg-accent-50 text-accent-600"
                        : "bg-danger/10 text-danger"
                    }`}
                  >
                    {userDetails?.status ? "Active" : "Inactive"}
                  </span>
                </div> */}
                </View>
              )}
            </CardContent>
          </Card>

          {/* User Details Card */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium w-1/3">Name</TableCell>
                    <TableCell>{userDetails?.name || "N/A"}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Gender</TableCell>
                    <TableCell>{userDetails?.gender || "N/A"}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Date of Birth</TableCell>
                    <TableCell>
                      {userDetails?.DOB
                        ? dayjs(userDetails?.DOB).format(DATE_FORMAT)
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Age</TableCell>
                    <TableCell>
                      {userDetails?.age ? userDetails?.age + " years" : "N/A"}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      Marital Status
                    </TableCell>
                    <TableCell>
                      {userDetails?.marital_status || "N/A"}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Email</TableCell>
                    <TableCell>
                      {" "}
                      <a href={`mailto:${userDetails?.email}`}>
                        {userDetails?.email || "N/A"}
                      </a>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Phone Number</TableCell>
                    <TableCell>
                      <a href={`tel:${userDetails?.phone}`}>
                        {userDetails?.phone || "N/A"}
                      </a>
                    </TableCell>
                  </TableRow>
                  {userDetails?.id_type && (
                    <TableRow>
                      <TableCell className="font-medium">
                        {userDetails?.id_type || "N/A"}
                      </TableCell>
                      <TableCell>
                        {userDetails?.id_number_masked || "N/A"}
                      </TableCell>
                    </TableRow>
                  )}
                  <TableRow>
                    <TableCell className="font-medium">Address</TableCell>
                    <TableCell>{userDetails?.addres || "N/A"}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">City</TableCell>
                    <TableCell>{userDetails?.city || "N/A"}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">State</TableCell>
                    <TableCell>{userDetails?.state || "N/A"}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Country</TableCell>
                    <TableCell>{userDetails?.country || "N/A"}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>

              <View className="mt-6">
                <CardTitle className="mb-4">Professional Information</CardTitle>
                <Table>
                  <TableBody>
                    {/* <TableRow>
                      <TableCell className="font-medium w-1/3">Role</TableCell>
                      <TableCell>{formatEnumValue(userDetails?.role)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Department</TableCell>
                      <TableCell>{userDetails?.department}</TableCell>
                    </TableRow> */}
                    <TableRow>
                      <TableCell className="font-medium">Designation</TableCell>
                      <TableCell>{userDetails?.designation || "N/A"}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">
                        Qualification
                      </TableCell>
                      <TableCell>
                        {userDetails?.qualification || "N/A"}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Department</TableCell>
                      <TableCell>
                        {userDetails?.department_name || "N/A"}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </View>
            </CardContent>
          </Card>
        </View>
      </View>
    </View>
  );
};

export default UserDetailPage;
