import View from "@/components/view";
import SectionOne from "./sectionOne";
import Text from "@/components/text";
import SectionTwo from "./sectionTwo";
import Button from "@/components/button";
import { useUsers } from "@/actions/calls/user";
import { validationForm } from "./validationForm";
import { toast } from "@/components/ui/use-toast";
import { UserInterface } from "@/interfaces/users";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FormTypeProps } from "@/interfaces/dashboard";
import { useNavigate, useParams } from "react-router-dom";
import { clearUserDetailsSlice } from "@/actions/slices/userSlice";
import useForm from "@/utils/custom-hooks/use-form";
import { imageUpload } from "@/actions/calls/uesImage";
import { RootState } from "@/actions/store";
import BouncingLoader from "../../../components/BouncingLoader";
// import WebcamCapture from "@/components/Capture";
// import ResetUserPassword from "@/components/resetUserPassword";

const Register: React.FC<FormTypeProps> = ({ formType = "add" }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addUser, getUserDetails, updateUser, cleanUp } = useUsers();
  const [errors, setErrors] = useState<Record<string, string>>({});
  // const [image, setImage] = useState<File[] | string[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const token = useSelector((state: any) => state.authentication.tokenStatus);
  const userDetails = useSelector((state: any) => state.users.userDetails);
  const userDetailData = {...userDetails,  id_edited: false};
  const { values: formValues, onSetHandler } = useForm<UserInterface>(
    userDetailData
  );

  const settings = useSelector((state: RootState) => state.systemSettings.settings);

  useEffect(() => {
    if (formType === "edit" && id) {
      getUserDetails(id, () => {}, [], (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      });
    }
    return () => {
      cleanUp();
      dispatch(clearUserDetailsSlice());
    };
  }, [id]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const userFormObj: Partial<UserInterface> = {};
    if(!settings?.id){
      return
    }

    try {
      for (let [key, value] of formData.entries()) {
        userFormObj[key as keyof UserInterface] = value as any;
      }
      userFormObj["system_settings_id"] = Number(settings?.id) as number;
      userFormObj["consent"] =
        userFormObj["consent" as keyof UserInterface] === "true" ? true : false;
        userFormObj["image"] = formValues?.image;
        userFormObj["id_edited"] = formValues?.id_value?.includes("X") && formValues?.id_value?.includes("x") ? false :  formValues?.id_edited;

         console.log("userFormObj 2", formValues);
            console.log("userFormObj 3", userFormObj);

      await validationForm.validate(userFormObj, {
        abortEarly: false,
        context: {
          userDetails,
          isEditMode: formType === "edit", // Pass edit mode flag
        },
      });
            console.log("userFormObj 2", formValues);
            console.log("userFormObj 3", userFormObj);

      delete userFormObj["image"];

      // return;
      setErrors({});
      setIsSubmitting(true);
      if (formType === "add") {
        addUser(userFormObj, (success, response) => {
          setIsSubmitting(false);
          if (success) {
            navigate(-1);
            toast({
              title: "Success!",
              description: " registration submitted successfully.",
              variant: "success",
            });     
            const userId = response?.data?.id;
            
            // console.log("userId", userId);
            if (userId && formValues?.image) {
              const imageUploadData = {
                id: userId,
                modal_type: "user_address_proof",
                file_name: "image",
                folder_name: "user_address_image",
                image: formValues?.image,
              };
              imageUpload(imageUploadData, (success, _) => {
                if (success) {
                  toast({
                    title: "Success!",
                    description: "File uploaded successfully",
                    variant: "success",
                  });
                } else {
                  toast({
                    title: "Error!",
                    description: "Failed to upload file",
                    variant: "destructive",
                  });
                }
              });
            }
          } else {
            toast({
              title: "Error!",
              description: response?.message,
              variant: "destructive",
            });
          }
        });
      } else if (id) {
        updateUser(id, userFormObj, (success: boolean, response: any) => {
          if (success) {
            
            toast({
              title: "Success!",
              variant: "success",
              // description: response?.message ?? "User Updated successfully.",
            });
            const userId = response?.data?.id;
            if (userId &&formValues?.image) {
              const imageUploadData = {
                id: userId,
                modal_type: "user_address_proof",
                file_name: "image",
                folder_name: "user_address_image",
                image: formValues?.image,
              };
              imageUpload(imageUploadData, (success, _) => {
                if (success) {
                  toast({
                    title: "Success!",
                    description: "File uploaded successfully",
                    variant: "success",
                  });
                  // setIsSubmitting(false);
                } else {
                  toast({
                    title: "Error!",
                    description: "Failed to upload file",
                    variant: "destructive",
                  });
                }
              });
            }

          } else {
            toast({
              title: "Error!",
              variant: "destructive",
              // description: response?.message,
            });
          }
          setIsSubmitting(false);
          navigate(-1);
        });
      }
    } catch (error: any) {
      console.error("Validation Error:", error);
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };

  return (
    <View className="min-h-screen dark:bg-background flex flex-col justify-center items-center p-4">
      <BouncingLoader isLoading={isLoading} />
      {/* Header */}
      {!token && (
        <View className="text-center mb-6">
          <Text
            as="h1"
            className="text-primary-600 text-3xl md:text-4xl font-bold"
          >
            {import.meta.env.VITE_HOSPITAL_NAME}
          </Text>
          <Text as="p" className="text-text-light mt-1">
            {import.meta.env.VITE_TYPE_OF_APPLICATION}
          </Text>
        </View>
      )}

      {/* Registration Card */}
      <View className="bg-white dark:bg-slate-800 border-0 rounded-xl shadow-medium w-full max-w-5xl p-8 md:p-10 mb-8">
        <View className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <View>
            <Text
              as="h2"
              weight="font-bold"
              className="text-3xl font-bold text-slate-900 dark:text-white mb-2"
            >
              {formType === "add" ? "Register New User" : "Edit User"}
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-lg">
              {formType === "add" ? "Fill in the details to create a new account" : "Update user information"}
            </Text>
          </View>
          <Button
            onPress={() => navigate(-1)}
            variant="outline"
            className="px-6 py-2 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back
          </Button>
        </View>

        <form onSubmit={handleSubmit}>
          {/* <View className="grid grid-cols-2 md:grid-cols-1 gap-6"> */}
          {/* Personal Information Section */}
          <View className="col-span-2 mb-6">
            <View className="border-b border-slate-200 dark:border-slate-700 pb-4">
              <Text
                as="h3"
                className="text-xl font-semibold text-slate-900 dark:text-white mb-2"
              >
                Personal Information
              </Text>
              <Text as="p" className="text-slate-600 dark:text-slate-400">
                Basic personal details and identification
              </Text>
            </View>
          </View>

          <SectionOne
            errorsDOB={errors.DOB}
            errorsName={errors.name}
            errorsEmail={errors.email}
            errorsPhone={errors.phone}
            errorsGender={errors.gender}
            errorsAge={errors.age}
            errorsMaritalStatus={errors.marital_status}
            errorsIds={errors.id_type}
            errorsIdValue={errors.id_value}
            errorConsent={errors.consent}
            errorsImage={errors.image}
            errorsIdProofForPan={errors.id_proof_for_pan}
            formType={formType}
            setImage={onSetHandler}
          />

          {/* Address Section */}
          {/* <View className="col-span-2">
              <Text
                as="h3"
                className="text-lg font-semibold text-text-DEFAULT mb-3 border-b border-neutral-200 pb-2"
              >
                Address Information
              </Text>
            </View> */}

          {/* Address */}
          <SectionTwo
            formType={formType}
            errorsAddress={errors.address}
            errorsCity={errors.city}
            errorsState={errors.state}
            errorsCountry={errors.country}
            errorsPinCode={errors.pincode}
            errorsRole={errors.role}
            errorsDesignation={errors.designation}
            errorsQualification={errors.qualification}
            errorsDepartment={errors.department}
          />

          {/* Submit Button - spans full width */}
          <View className="col-span-2 mt-6">
            <Button
              htmlType="submit"
              loading={isSubmitting}
              className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </View>
          {/* </View> */}
        </form>
      </View>

      {/* Footer */}
      {!token && (
        <View className="mt-4 text-center text-text-lighter text-sm pb-6">
          © {new Date().getFullYear()} MedCare Hospital Management System. All
          rights reserved.
        </View>
      )}
    </View>
  );
};

export default Register;
