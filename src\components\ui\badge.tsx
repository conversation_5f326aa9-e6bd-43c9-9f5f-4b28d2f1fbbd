import * as React from "react";
import View from "../view";

const badgeBaseClasses = "inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium transition-all duration-200";

const badgeVariantClasses: Record<string, string> = {
  default: "bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300",
  success: "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300",
  secondary: "bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300",
  destructive: "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",
  warning: "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300",
  outline: "border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 bg-transparent",
};

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: keyof typeof badgeVariantClasses; 
  className?: string;
  style?: React.CSSProperties;
}

function Badge({ className = '', variant = 'default', style, ...props }: BadgeProps) {
  const variantClass = badgeVariantClasses[variant] || badgeVariantClasses.default;

  return (
    <View className={`${badgeBaseClasses} ${variantClass} ${className}`} style={style}{...props} />
  );
}

export { Badge };
