import { RadioProps, RadioGroupProps } from "@/interfaces/components/input/radioButtons";
import View from "./view";
import Input from "./input";
import Text from "./text";

const Radio: React.FC<RadioProps> = ({
  value,
  label,
  name,
  checked = false,
  disabled = false,
  onChange,
  radioSize = "medium",
  variant = "default",
  id,
  style,
  className,
  ...rest
}) => {
  const radioId = id || `radio-${name}-${value}`;

  return (
    <View
      className={`flex items-center space-x-2 ${disabled ? "opacity-60 cursor-not-allowed" : "cursor-pointer"} ${className || ""}`}
      style={style}
    >
      <input
        type="radio"
        id={radioId}
        name={name}
        value={value}
        checked={checked}
        disabled={disabled}
        onChange={onChange}
        className="
          h-4 w-4 text-primary-600 bg-white dark:bg-slate-800
          border-2 border-slate-300 dark:border-slate-600
          focus:ring-2 focus:ring-primary-500/20 dark:focus:ring-primary-400/20
          focus:border-primary-500 dark:focus:border-primary-400
          checked:bg-primary-600 checked:border-primary-600
          dark:checked:bg-primary-500 dark:checked:border-primary-500
          transition-all duration-200 cursor-pointer
        "
        {...rest}
      />
      <label htmlFor={radioId} className="text-sm font-medium text-slate-700 dark:text-slate-300 cursor-pointer">
        {label}
      </label>
    </View>
  );
};

const RadioGroup: React.FC<RadioGroupProps> = ({
  options = [],
  name,
  value,
  defaultValue,
  onChange,
  disabled = false,
  direction = "vertical",
  radioSize = "medium",
  variant = "default",
  id,
  style,
  className,
  ...rest
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(event.target.value);
    }
  };

  return (
    <View
      className={`flex ${direction === 'horizontal' ? 'flex-row space-x-4' : 'flex-col space-y-2'} ${className || ""}`}
      role="radiogroup"
      aria-label={rest["aria-label"]}
      id={id}
      style={style}
    >
      {options.map((option) => (
        <Radio
          key={option.value}
          name={name}
          value={option.value}
          label={option.label}
          checked={value !== undefined ? value === option.value : defaultValue === option.value}
          disabled={disabled || option.disabled}
          onChange={handleChange}
          radioSize={radioSize}
          variant={variant}
        />
      ))}
    </View>
  );
};

export { Radio, RadioGroup };