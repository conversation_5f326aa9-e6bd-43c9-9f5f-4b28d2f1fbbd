{"version": 3, "file": "svg.resize.iife.js", "sources": ["../src/ResizeHandler.js", "../src/svg.resize.js"], "sourcesContent": ["import { Point } from '@svgdotjs/svg.js'\nimport { Matrix } from '@svgdotjs/svg.js'\nimport { on, off, Box } from '@svgdotjs/svg.js'\n\nconst getCoordsFromEvent = (ev) => {\n  if (ev.changedTouches) {\n    ev = ev.changedTouches[0]\n  }\n  return { x: ev.clientX, y: ev.clientY }\n}\n\nconst maxBoxFromPoints = (points) => {\n  let x = Infinity\n  let y = Infinity\n  let x2 = -Infinity\n  let y2 = -Infinity\n\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]\n    x = Math.min(x, p[0])\n    y = Math.min(y, p[1])\n    x2 = Math.max(x2, p[0])\n    y2 = Math.max(y2, p[1])\n  }\n\n  return new Box(x, y, x2 - x, y2 - y)\n}\n\nfunction scaleBox(box, origin, scale) {\n  const points = [\n    [box.x, box.y],\n    [box.x + box.width, box.y],\n    [box.x + box.width, box.y + box.height],\n    [box.x, box.y + box.height],\n  ]\n\n  const newPoints = points.map(([x, y]) => {\n    // Translate to origin\n    const translatedX = x - origin[0]\n    const translatedY = y - origin[1]\n\n    // Scale\n    const scaledX = translatedX * scale\n    const scaledY = translatedY * scale\n\n    // Translate back\n    return [scaledX + origin[0], scaledY + origin[1]]\n  })\n\n  return maxBoxFromPoints(newPoints)\n}\n\nexport class ResizeHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_ResizeHandler', this)\n    this.lastCoordinates = null\n    this.eventType = ''\n    this.lastEvent = null\n    this.handleResize = this.handleResize.bind(this)\n    this.resize = this.resize.bind(this)\n    this.endResize = this.endResize.bind(this)\n    this.rotate = this.rotate.bind(this)\n    this.movePoint = this.movePoint.bind(this)\n  }\n\n  active(value, options) {\n    this.preserveAspectRatio = options.preserveAspectRatio ?? false\n    this.aroundCenter = options.aroundCenter ?? false\n    this.grid = options.grid ?? 0\n    this.degree = options.degree ?? 0\n\n    // remove all resize events\n    this.el.off('.resize')\n\n    if (!value) return\n\n    this.el.on(\n      [\n        'lt.resize',\n        'rt.resize',\n        'rb.resize',\n        'lb.resize',\n        't.resize',\n        'r.resize',\n        'b.resize',\n        'l.resize',\n        'rot.resize',\n        'point.resize',\n      ],\n      this.handleResize\n    )\n\n    // in case the options were changed mid-resize,\n    // we have to replay the last event to see the immediate effect of the option change\n    if (this.lastEvent) {\n      if (this.eventType === 'rot') {\n        this.rotate(this.lastEvent)\n      } else if (this.eventType === 'point') {\n        this.movePoint(this.lastEvent)\n      } else {\n        this.resize(this.lastEvent)\n      }\n    }\n  }\n\n  // This is called when a user clicks on one of the resize points\n  handleResize(e) {\n    this.eventType = e.type\n    const { event, index, points } = e.detail\n    const isMouse = !event.type.indexOf('mouse')\n\n    // Check for left button\n    if (isMouse && (event.which || event.buttons) !== 1) {\n      return\n    }\n\n    // Fire beforedrag event\n    if (this.el.dispatch('beforeresize', { event: e, handler: this }).defaultPrevented) {\n      return\n    }\n\n    this.box = this.el.bbox()\n    this.startPoint = this.el.point(getCoordsFromEvent(event))\n    this.index = index\n    this.points = points.slice()\n\n    // We consider the resize done, when a touch is canceled, too\n    const eventMove = (isMouse ? 'mousemove' : 'touchmove') + '.resize'\n    const eventEnd = (isMouse ? 'mouseup' : 'touchcancel.resize touchend') + '.resize'\n\n    if (e.type === 'point') {\n      on(window, eventMove, this.movePoint)\n    } else if (e.type === 'rot') {\n      on(window, eventMove, this.rotate)\n    } else {\n      on(window, eventMove, this.resize)\n    }\n    on(window, eventEnd, this.endResize)\n  }\n\n  resize(e) {\n    this.lastEvent = e\n\n    const endPoint = this.snapToGrid(this.el.point(getCoordsFromEvent(e)))\n\n    let dx = endPoint.x - this.startPoint.x\n    let dy = endPoint.y - this.startPoint.y\n\n    if (this.preserveAspectRatio && this.aroundCenter) {\n      dx *= 2\n      dy *= 2\n    }\n\n    const x = this.box.x + dx\n    const y = this.box.y + dy\n    const x2 = this.box.x2 + dx\n    const y2 = this.box.y2 + dy\n\n    let box = new Box(this.box)\n\n    if (this.eventType.includes('l')) {\n      box.x = Math.min(x, this.box.x2)\n      box.x2 = Math.max(x, this.box.x2)\n    }\n\n    if (this.eventType.includes('r')) {\n      box.x = Math.min(x2, this.box.x)\n      box.x2 = Math.max(x2, this.box.x)\n    }\n\n    if (this.eventType.includes('t')) {\n      box.y = Math.min(y, this.box.y2)\n      box.y2 = Math.max(y, this.box.y2)\n    }\n\n    if (this.eventType.includes('b')) {\n      box.y = Math.min(y2, this.box.y)\n      box.y2 = Math.max(y2, this.box.y)\n    }\n\n    box.width = box.x2 - box.x\n    box.height = box.y2 - box.y\n\n    // after figuring out the resulting box,\n    // we have to check if the aspect ratio should be preserved\n    // if so, we have to find the correct scaling factor and scale the box around a fixed point (usually the opposite of the handle)\n    // in case aroundCenter is active, the fixed point is the center of the box\n    if (this.preserveAspectRatio) {\n      const scaleX = box.width / this.box.width\n      const scaleY = box.height / this.box.height\n\n      const order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l']\n\n      const origin = (order.indexOf(this.eventType) + 4) % order.length\n      const constantPoint = this.aroundCenter ? [this.box.cx, this.box.cy] : this.points[origin]\n\n      let scale = this.eventType.includes('t') || this.eventType.includes('b') ? scaleY : scaleX\n      scale = this.eventType.length === 2 ? Math.max(scaleX, scaleY) : scale\n\n      box = scaleBox(this.box, constantPoint, scale)\n    }\n\n    if (\n      this.el.dispatch('resize', {\n        box: new Box(box),\n        angle: 0,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.size(box.width, box.height).move(box.x, box.y)\n  }\n\n  movePoint(e) {\n    this.lastEvent = e\n    const { x, y } = this.snapToGrid(this.el.point(getCoordsFromEvent(e)))\n    const pointArr = this.el.array().slice()\n    pointArr[this.index] = [x, y]\n\n    if (\n      this.el.dispatch('resize', {\n        box: maxBoxFromPoints(pointArr),\n        angle: 0,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.plot(pointArr)\n  }\n\n  rotate(e) {\n    this.lastEvent = e\n\n    const startPoint = this.startPoint\n    const endPoint = this.el.point(getCoordsFromEvent(e))\n\n    const { cx, cy } = this.box\n\n    const dx1 = startPoint.x - cx\n    const dy1 = startPoint.y - cy\n\n    const dx2 = endPoint.x - cx\n    const dy2 = endPoint.y - cy\n\n    const c = Math.sqrt(dx1 * dx1 + dy1 * dy1) * Math.sqrt(dx2 * dx2 + dy2 * dy2)\n\n    if (c === 0) {\n      return\n    }\n    let angle = (Math.acos((dx1 * dx2 + dy1 * dy2) / c) / Math.PI) * 180\n\n    // catches 0 angle and NaN angle that are zero as well (but numerically instable)\n    if (!angle) return\n\n    if (endPoint.x < startPoint.x) {\n      angle = -angle\n    }\n\n    const matrix = new Matrix(this.el)\n    const { x: ox, y: oy } = new Point(cx, cy).transformO(matrix)\n\n    const { rotate } = matrix.decompose()\n    const resultAngle = this.snapToAngle(rotate + angle) - rotate\n\n    if (\n      this.el.dispatch('resize', {\n        box: this.box,\n        angle: resultAngle,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.transform(matrix.rotateO(resultAngle, ox, oy))\n  }\n\n  endResize(ev) {\n    // Unbind resize and end events to window\n    if (this.eventType !== 'rot' && this.eventType !== 'point') {\n      this.resize(ev)\n    }\n\n    this.lastEvent = null\n\n    this.eventType = ''\n    off(window, 'mousemove.resize touchmove.resize')\n    off(window, 'mouseup.resize touchend.resize')\n  }\n\n  snapToGrid(point) {\n    if (this.grid) {\n      point.x = Math.round(point.x / this.grid) * this.grid\n      point.y = Math.round(point.y / this.grid) * this.grid\n    }\n\n    return point\n  }\n\n  snapToAngle(angle) {\n    if (this.degree) {\n      angle = Math.round(angle / this.degree) * this.degree\n    }\n\n    return angle\n  }\n}\n", "import { extend, Element } from '@svgdotjs/svg.js'\nimport { ResizeHandler } from './ResizeHandler'\n\nextend(Element, {\n  // Resize element with mouse\n  resize: function (enabled = true, options = {}) {\n    if (typeof enabled === 'object') {\n      options = enabled\n      enabled = true\n    }\n\n    let resizeHandler = this.remember('_ResizeHandler')\n\n    if (!resizeHandler) {\n      if (enabled.prototype instanceof ResizeHandler) {\n        /* eslint new-cap: [\"error\", { \"newIsCap\": false }] */\n        resizeHandler = new enabled(this)\n        enabled = true\n      } else {\n        resizeHandler = new ResizeHandler(this)\n      }\n\n      this.remember('_resizeHandler', resizeHandler)\n    }\n\n    resizeHandler.active(enabled, options)\n\n    return this\n  },\n})\n\nexport { ResizeHandler }\n"], "names": ["getCoordsFromEvent", "ev", "changedTouches", "x", "clientX", "y", "clientY", "maxBoxFromPoints", "points", "Infinity", "x2", "y2", "i", "length", "p", "Math", "min", "max", "Box", "ResizeHandler", "constructor", "el", "this", "remember", "lastCoordinates", "eventType", "lastEvent", "handleResize", "bind", "resize", "endResize", "rotate", "movePoint", "active", "value", "options", "preserveAspectRatio", "aroundCenter", "grid", "degree", "off", "on", "e", "type", "event", "index", "detail", "isMouse", "indexOf", "which", "buttons", "dispatch", "handler", "defaultPrevented", "box", "bbox", "startPoint", "point", "slice", "eventMove", "eventEnd", "window", "endPoint", "snapToGrid", "dx", "dy", "includes", "width", "height", "scaleX", "scaleY", "order", "origin", "constantPoint", "cx", "cy", "scale", "newPoints", "map", "translatedX", "scaledY", "scaleBox", "angle", "size", "move", "pointArr", "array", "plot", "dx1", "dy1", "dx2", "dy2", "c", "sqrt", "acos", "PI", "matrix", "Matrix", "ox", "oy", "Point", "transformO", "decompose", "resultAngle", "snapToAngle", "transform", "rotateO", "svg_js", "round", "extend", "Element", "enabled", "resize<PERSON><PERSON>ler", "prototype"], "mappings": ";0GAIM,MAAAA,EAAsBC,IACtBA,EAAGC,iBACAD,EAAAA,EAAGC,eAAe,IAElB,CAAEC,EAAGF,EAAGG,QAASC,EAAGJ,EAAGK,UAG1BC,EAAoBC,IACxB,IAAIL,EAAIM,IACJJ,EAAII,IACJC,GAAKD,IACLE,GAAKF,IAET,IAAA,IAASG,EAAI,EAAGA,EAAIJ,EAAOK,OAAQD,IAAK,CAChC,MAAAE,EAAIN,EAAOI,GACjBT,EAAIY,KAAKC,IAAIb,EAAGW,EAAE,IAClBT,EAAIU,KAAKC,IAAIX,EAAGS,EAAE,IAClBJ,EAAKK,KAAKE,IAAIP,EAAII,EAAE,IACpBH,EAAKI,KAAKE,IAAIN,EAAIG,EAAE,GACrB,CAEM,OAAA,IAAII,EAAGA,IAACf,EAAGE,EAAGK,EAAKP,EAAGQ,EAAKN,EAAC,EA2B9B,MAAMc,EACX,WAAAC,CAAYC,GACVC,KAAKD,GAAKA,EACPA,EAAAE,SAAS,iBAAkBD,MAC9BA,KAAKE,gBAAkB,KACvBF,KAAKG,UAAY,GACjBH,KAAKI,UAAY,KACjBJ,KAAKK,aAAeL,KAAKK,aAAaC,KAAKN,MAC3CA,KAAKO,OAASP,KAAKO,OAAOD,KAAKN,MAC/BA,KAAKQ,UAAYR,KAAKQ,UAAUF,KAAKN,MACrCA,KAAKS,OAAST,KAAKS,OAAOH,KAAKN,MAC/BA,KAAKU,UAAYV,KAAKU,UAAUJ,KAAKN,KACtC,CAED,MAAAW,CAAOC,EAAOC,GACPb,KAAAc,oBAAsBD,EAAQC,sBAAuB,EACrDd,KAAAe,aAAeF,EAAQE,eAAgB,EACvCf,KAAAgB,KAAOH,EAAQG,MAAQ,EACvBhB,KAAAiB,OAASJ,EAAQI,QAAU,EAG3BjB,KAAAD,GAAGmB,IAAI,WAEPN,IAELZ,KAAKD,GAAGoB,GACN,CACE,YACA,YACA,YACA,YACA,WACA,WACA,WACA,WACA,aACA,gBAEFnB,KAAKK,cAKHL,KAAKI,YACgB,QAAnBJ,KAAKG,UACFH,KAAAS,OAAOT,KAAKI,WACW,UAAnBJ,KAAKG,UACTH,KAAAU,UAAUV,KAAKI,WAEfJ,KAAAO,OAAOP,KAAKI,YAGtB,CAGD,YAAAC,CAAae,GACXpB,KAAKG,UAAYiB,EAAEC,KACnB,MAAMC,MAAEA,EAAAC,MAAOA,EAAOrC,OAAAA,GAAWkC,EAAEI,OAC7BC,GAAWH,EAAMD,KAAKK,QAAQ,SAGpC,GAAID,GAA8C,KAAlCH,EAAMK,OAASL,EAAMM,SACnC,OAIE,GAAA5B,KAAKD,GAAG8B,SAAS,eAAgB,CAAEP,MAAOF,EAAGU,QAAS9B,OAAQ+B,iBAChE,OAGG/B,KAAAgC,IAAMhC,KAAKD,GAAGkC,OACnBjC,KAAKkC,WAAalC,KAAKD,GAAGoC,MAAMzD,EAAmB4C,IACnDtB,KAAKuB,MAAQA,EACRvB,KAAAd,OAASA,EAAOkD,QAGf,MAAAC,GAAaZ,EAAU,YAAc,aAAe,UACpDa,GAAYb,EAAU,UAAY,+BAAiC,UAE1D,UAAXL,EAAEC,KACJF,EAAAA,GAAGoB,OAAQF,EAAWrC,KAAKU,WACP,QAAXU,EAAEC,KACXF,EAAAA,GAAGoB,OAAQF,EAAWrC,KAAKS,QAE3BU,EAAAA,GAAGoB,OAAQF,EAAWrC,KAAKO,QAE7BY,EAAAA,GAAGoB,OAAQD,EAAUtC,KAAKQ,UAC3B,CAED,MAAAD,CAAOa,GACLpB,KAAKI,UAAYgB,EAEX,MAAAoB,EAAWxC,KAAKyC,WAAWzC,KAAKD,GAAGoC,MAAMzD,EAAmB0C,KAElE,IAAIsB,EAAKF,EAAS3D,EAAImB,KAAKkC,WAAWrD,EAClC8D,EAAKH,EAASzD,EAAIiB,KAAKkC,WAAWnD,EAElCiB,KAAKc,qBAAuBd,KAAKe,eAC7B2B,GAAA,EACAC,GAAA,GAGF,MAAA9D,EAAImB,KAAKgC,IAAInD,EAAI6D,EACjB3D,EAAIiB,KAAKgC,IAAIjD,EAAI4D,EACjBvD,EAAKY,KAAKgC,IAAI5C,GAAKsD,EACnBrD,EAAKW,KAAKgC,IAAI3C,GAAKsD,EAEzB,IAAIX,EAAM,IAAIpC,MAAII,KAAKgC,KA6BvB,GA3BIhC,KAAKG,UAAUyC,SAAS,OAC1BZ,EAAInD,EAAIY,KAAKC,IAAIb,EAAGmB,KAAKgC,IAAI5C,IAC7B4C,EAAI5C,GAAKK,KAAKE,IAAId,EAAGmB,KAAKgC,IAAI5C,KAG5BY,KAAKG,UAAUyC,SAAS,OAC1BZ,EAAInD,EAAIY,KAAKC,IAAIN,EAAIY,KAAKgC,IAAInD,GAC9BmD,EAAI5C,GAAKK,KAAKE,IAAIP,EAAIY,KAAKgC,IAAInD,IAG7BmB,KAAKG,UAAUyC,SAAS,OAC1BZ,EAAIjD,EAAIU,KAAKC,IAAIX,EAAGiB,KAAKgC,IAAI3C,IAC7B2C,EAAI3C,GAAKI,KAAKE,IAAIZ,EAAGiB,KAAKgC,IAAI3C,KAG5BW,KAAKG,UAAUyC,SAAS,OAC1BZ,EAAIjD,EAAIU,KAAKC,IAAIL,EAAIW,KAAKgC,IAAIjD,GAC9BiD,EAAI3C,GAAKI,KAAKE,IAAIN,EAAIW,KAAKgC,IAAIjD,IAG7BiD,EAAAa,MAAQb,EAAI5C,GAAK4C,EAAInD,EACrBmD,EAAAc,OAASd,EAAI3C,GAAK2C,EAAIjD,EAMtBiB,KAAKc,oBAAqB,CAC5B,MAAMiC,EAASf,EAAIa,MAAQ7C,KAAKgC,IAAIa,MAC9BG,EAAShB,EAAIc,OAAS9C,KAAKgC,IAAIc,OAE/BG,EAAQ,CAAC,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,KAEhDC,GAAUD,EAAMvB,QAAQ1B,KAAKG,WAAa,GAAK8C,EAAM1D,OACrD4D,EAAgBnD,KAAKe,aAAe,CAACf,KAAKgC,IAAIoB,GAAIpD,KAAKgC,IAAIqB,IAAMrD,KAAKd,OAAOgE,GAE/E,IAAAI,EAAQtD,KAAKG,UAAUyC,SAAS,MAAQ5C,KAAKG,UAAUyC,SAAS,KAAOI,EAASD,EAC5EO,EAA0B,IAA1BtD,KAAKG,UAAUZ,OAAeE,KAAKE,IAAIoD,EAAQC,GAAUM,EAEjEtB,EA5KG,SAASA,EAAKkB,EAAQI,GAC7B,MAOMC,EAPS,CACb,CAACvB,EAAInD,EAAGmD,EAAIjD,GACZ,CAACiD,EAAInD,EAAImD,EAAIa,MAAOb,EAAIjD,GACxB,CAACiD,EAAInD,EAAImD,EAAIa,MAAOb,EAAIjD,EAAIiD,EAAIc,QAChC,CAACd,EAAInD,EAAGmD,EAAIjD,EAAIiD,EAAIc,SAGGU,KAAI,EAAE3E,EAAGE,MAE1B,MAAA0E,EAAc5E,EAAIqE,EAAO,GAKzBQ,GAJc3E,EAAImE,EAAO,IAIDI,EAGvB,MAAA,CAJSG,EAAcH,EAIZJ,EAAO,GAAIQ,EAAUR,EAAO,GAAE,IAGlD,OAAOjE,EAAiBsE,EAC1B,CAsJYI,CAAS3D,KAAKgC,IAAKmB,EAAeG,EACzC,CAGCtD,KAAKD,GAAG8B,SAAS,SAAU,CACzBG,IAAK,IAAIpC,EAAGA,IAACoC,GACb4B,MAAO,EACPzD,UAAWH,KAAKG,UAChBmB,MAAOF,EACPU,QAAS9B,OACR+B,kBAKA/B,KAAAD,GAAG8D,KAAK7B,EAAIa,MAAOb,EAAIc,QAAQgB,KAAK9B,EAAInD,EAAGmD,EAAIjD,EACrD,CAED,SAAA2B,CAAUU,GACRpB,KAAKI,UAAYgB,EACjB,MAAMvC,EAAEA,EAAAE,EAAGA,GAAMiB,KAAKyC,WAAWzC,KAAKD,GAAGoC,MAAMzD,EAAmB0C,KAC5D2C,EAAW/D,KAAKD,GAAGiE,QAAQ5B,QACjC2B,EAAS/D,KAAKuB,OAAS,CAAC1C,EAAGE,GAGzBiB,KAAKD,GAAG8B,SAAS,SAAU,CACzBG,IAAK/C,EAAiB8E,GACtBH,MAAO,EACPzD,UAAWH,KAAKG,UAChBmB,MAAOF,EACPU,QAAS9B,OACR+B,kBAKA/B,KAAAD,GAAGkE,KAAKF,EACd,CAED,MAAAtD,CAAOW,GACLpB,KAAKI,UAAYgB,EAEjB,MAAMc,EAAalC,KAAKkC,WAClBM,EAAWxC,KAAKD,GAAGoC,MAAMzD,EAAmB0C,KAE5CgC,GAAEA,EAAAC,GAAIA,GAAOrD,KAAKgC,IAElBkC,EAAMhC,EAAWrD,EAAIuE,EACrBe,EAAMjC,EAAWnD,EAAIsE,EAErBe,EAAM5B,EAAS3D,EAAIuE,EACnBiB,EAAM7B,EAASzD,EAAIsE,EAEnBiB,EAAI7E,KAAK8E,KAAKL,EAAMA,EAAMC,EAAMA,GAAO1E,KAAK8E,KAAKH,EAAMA,EAAMC,EAAMA,GAEzE,GAAU,IAANC,EACF,OAEE,IAAAV,EAASnE,KAAK+E,MAAMN,EAAME,EAAMD,EAAME,GAAOC,GAAK7E,KAAKgF,GAAM,IAGjE,IAAKb,EAAO,OAERpB,EAAS3D,EAAIqD,EAAWrD,IAC1B+E,GAASA,GAGX,MAAMc,EAAS,IAAIC,SAAO3E,KAAKD,KACvBlB,EAAG+F,EAAI7F,EAAG8F,GAAO,IAAIC,EAAKA,MAAC1B,EAAIC,GAAI0B,WAAWL,IAEhDjE,OAAEA,GAAWiE,EAAOM,YACpBC,EAAcjF,KAAKkF,YAAYzE,EAASmD,GAASnD,EAGrDT,KAAKD,GAAG8B,SAAS,SAAU,CACzBG,IAAKhC,KAAKgC,IACV4B,MAAOqB,EACP9E,UAAWH,KAAKG,UAChBmB,MAAOF,EACPU,QAAS9B,OACR+B,kBAKL/B,KAAKD,GAAGoF,UAAUT,EAAOU,QAAQH,EAAaL,EAAIC,GACnD,CAED,SAAArE,CAAU7B,GAEe,QAAnBqB,KAAKG,WAA0C,UAAnBH,KAAKG,WACnCH,KAAKO,OAAO5B,GAGdqB,KAAKI,UAAY,KAEjBJ,KAAKG,UAAY,GACdkF,EAAAnE,IAACqB,OAAQ,qCACT8C,EAAAnE,IAACqB,OAAQ,iCACb,CAED,UAAAE,CAAWN,GAMF,OALHnC,KAAKgB,OACDmB,EAAAtD,EAAIY,KAAK6F,MAAMnD,EAAMtD,EAAImB,KAAKgB,MAAQhB,KAAKgB,KAC3CmB,EAAApD,EAAIU,KAAK6F,MAAMnD,EAAMpD,EAAIiB,KAAKgB,MAAQhB,KAAKgB,MAG5CmB,CACR,CAED,WAAA+C,CAAYtB,GAKH,OAJH5D,KAAKiB,SACP2C,EAAQnE,KAAK6F,MAAM1B,EAAQ5D,KAAKiB,QAAUjB,KAAKiB,QAG1C2C,CACR,SCzTGyB,EAAAE,OAACC,UAAS,CAEdjF,OAAQ,SAAUkF,GAAU,EAAM5E,EAAU,CAAA,GACnB,iBAAZ4E,IACC5E,EAAA4E,EACAA,GAAA,GAGR,IAAAC,EAAgB1F,KAAKC,SAAS,kBAgB3B,OAdFyF,IACCD,EAAQE,qBAAqB9F,GAEf6F,EAAA,IAAID,EAAQzF,MAClByF,GAAA,GAEMC,EAAA,IAAI7F,EAAcG,MAG/BA,KAAAC,SAAS,iBAAkByF,IAGpBA,EAAA/E,OAAO8E,EAAS5E,GAEvBb,IACR"}