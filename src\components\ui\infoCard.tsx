import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 relative hover:shadow-medium transition-all duration-200 ${className}`}
    style={style}
  >
    <View className="flex items-center justify-between mb-3">
      <Text
        as="h3"
        className={`text-sm font-medium text-slate-600 dark:text-slate-400 ${
          titleStyle ? titleStyle : ""
        }`}
      >
        {label}
      </Text>
      {icon && (
        <View
          className={`p-2.5 rounded-xl bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 ${
            iconStyle ? iconStyle : ""
          }`}
        >
          {icon}
        </View>
      )}
    </View>
    <Text
      weight="font-bold"
      className={`text-2xl font-bold text-slate-900 dark:text-white mb-1 ${
        isLink ? "text-primary cursor-pointer hover:underline" : ""
      } ${valueStyle ? valueStyle : ""}`}
    >
      {value}
    </Text>
    {subValue && (
      <Text
        className={`text-sm text-slate-500 dark:text-slate-400 ${
          subValueStyle ? subValueStyle : ""
        }`}
      >
        {subValue}
      </Text>
    )}
  </View>
);

export default InfoCard;
