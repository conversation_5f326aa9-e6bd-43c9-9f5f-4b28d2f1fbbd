import dayjs from "dayjs";
import Filter from "@/pages/filter";
import Text from "@/components/text";
import View from "@/components/view";
import Input from "@/components/input";
import { Link } from "react-router-dom";
import <PERSON><PERSON> from "@/components/button";
import { RootState } from "@/actions/store";
import { Card } from "@/components/ui/card";
import DataSort from "@/components/SortData";
import InfoCard from "@/components/ui/infoCard";
import { CircleDollarSign, TrendingUp, Calendar, FileText } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import SearchBar from "@/components/ui/search-bar";
import {
  REPORT,
  DATE_FORMAT,
  EXPENSES_TABLE_URL,
  EXPENSES_DETAILS_URL,
} from "@/utils/urls/frontend";
import { useDispatch, useSelector } from "react-redux";
import DynamicTable from "@/components/ui/DynamicTable";
import SingleSelector from "@/components/SingleSelector";
import BouncingLoader from "@/components/BouncingLoader";
import PaginationComponent from "@/components/Pagination";
import { useAmountType } from "@/actions/calls/amountType";
import { handleSortChange } from "@/utils/helperFunctions";
import DateRangePicker from "@/components/DateRangePicker";
import { clearList } from "@/actions/slices/expenseReport";
import { useExpenseReport } from "@/actions/calls/reports/expenses";
import { EXPENSE_REPORT_DOWNLOAD_URL } from "@/utils/urls/backend";
// import { Plus } from "lucide-react";
// import DataSort from "@/components/SortData";
// import ActionMenu from "@/components/editDeleteAction";

const Expense: React.FC<{}> = ({}) => {
  //   const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loadingStatus, setIsLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const expenseReportList = useSelector(
    (state: RootState) => state.expenseReport.expenseReportList
  );
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const { amountTypeDropdownHandler } = useAmountType();

  const { cleanUp, getListApi } = useExpenseReport();

  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDropdownData
  );

  const sortOptions: any[] = [
    { label: "Date (A-Z)", value: "date", order: "asc" },
    { label: "Date (Z-A)", value: "date", order: "desc" },
    { label: "Amount (A-Z)", value: "amount", order: "asc" },
    { label: "Amount (Z-A)", value: "amount", order: "desc" },
    { label: "Mode of payment (A-Z)", value: "mode_of_payment", order: "asc" },
    { label: "Mode of payment (Z-A)", value: "mode_of_payment", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<any | null>(sortOptions[0]);

  useEffect(() => {
    amountTypeDropdownHandler(() => {});
  }, []);

  useEffect(() => {
    getListApi(
      searchParams.get("page") ?? 1,
      () => {},
      (loadingStatus) => {
        setIsLoading(
          loadingStatus == "pending"
            ? true
            : loadingStatus == "failed"
            ? true
            : loadingStatus == "success" && false
        );
      },
      searchParams.get("search") ?? null,
      searchParams.get("sort_by") ?? null,
      searchParams.get("sort_order") ?? null,
      searchParams?.get("from_date") ?? null,
      searchParams?.get("to_date") ?? null,
      filterData
    );
    return () => {
      cleanUp();
      dispatch(clearList());
    };
  }, [
    filterData,
    searchParams.get("page"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams?.get("to_date"),
    searchParams?.get("from_date"),
    searchParams.get("sort_order"),
  ]);

  const downloadExpensesExcel = async () => {
    try {
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token");

      const response = await fetch(
        `${baseUrl}${EXPENSE_REPORT_DOWNLOAD_URL}?page=${
          searchParams.get("page") ?? 1
        }${
          searchParams.get("search")
            ? "&search=" + searchParams.get("search")
            : ""
        }${
          searchParams.get("sort_by")
            ? "&sort_by=" + searchParams.get("sort_by")
            : ""
        }${
          searchParams.get("sort_order")
            ? "&sort_order=" + searchParams.get("sort_order")
            : ""
        }${
          searchParams?.get("from_date")
            ? "&from_date=" + searchParams?.get("from_date")
            : ""
        }${
          searchParams?.get("to_date")
            ? "&to_date=" + searchParams?.get("to_date")
            : ""
        }`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Excel download failed");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "expenses-report.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Excel download error:", error);
      alert("Failed to download Excel report");
    }
  };

  return (
    <React.Fragment>
      <View className="fixed top-4 left-0  w-full z-50">
        <BouncingLoader isLoading={loadingStatus} />
      </View>
      {/* Header Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex items-center gap-3">
          <View className="p-2 rounded-lg bg-primary/10">
            <TrendingUp className="h-6 w-6 text-primary" />
          </View>
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1"
            >
              Expense Report
            </Text>
            <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
              Track and analyze hospital expenses and financial data
            </Text>
          </View>
        </View>
      </View>

      {/* Stats Cards */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <InfoCard
          label="Total Expenses"
          value={expenseReportList?.totalExpenses ?? "0"}
          valueStyle="!text-blue-600 dark:!text-blue-400"
          icon={<CircleDollarSign size={20} />}
          iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Total Records"
          value={expenseReportList?.table?.total || 0}
          valueStyle="!text-emerald-600 dark:!text-emerald-400"
          icon={<FileText size={20} />}
          iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="This Month"
          value={expenseReportList?.table?.data?.filter((expense: any) => {
            const currentMonth = new Date().getMonth();
            const expenseMonth = new Date(expense.date).getMonth();
            return expenseMonth === currentMonth;
          }).length || 0}
          valueStyle="!text-purple-600 dark:!text-purple-400"
          icon={<Calendar size={20} />}
          iconStyle="!bg-gradient-to-br !from-purple-100 !via-purple-200 !to-purple-300 dark:!from-purple-800/40 dark:!via-purple-700/40 dark:!to-purple-600/40 !text-purple-600 dark:!text-purple-400 !shadow-lg !shadow-purple-500/25 dark:!shadow-purple-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Average Amount"
          value={`₹${expenseReportList?.table?.data?.length > 0 ?
            (expenseReportList.table.data.reduce((sum: number, expense: any) => sum + (parseFloat(expense.amount) || 0), 0) / expenseReportList.table.data.length).toFixed(0) :
            '0'}`}
          valueStyle="!text-orange-600 dark:!text-orange-400"
          icon={<TrendingUp size={20} />}
          iconStyle="!bg-gradient-to-br !from-orange-100 !via-orange-200 !to-orange-300 dark:!from-orange-800/40 dark:!via-orange-700/40 dark:!to-orange-600/40 !text-orange-600 dark:!text-orange-400 !shadow-lg !shadow-orange-500/25 dark:!shadow-orange-400/20"
          className="hover:scale-[1.02] transition-transform duration-200"
        />
      </View>

      {/* Controls Section */}
      <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
        <View className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <View className="flex items-center gap-4">
            {expenseReportList?.table?.data?.length > 0 && (
              <Button
                variant="outline"
                onPress={downloadExpensesExcel}
                className="flex items-center gap-2"
              >
                <FileText size={16} />
                Download Report
              </Button>
            )}
          </View>
          <View>
            <Text
              as="label"
              className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
            >
              Select Date Range
            </Text>
            <DateRangePicker
              placeholder="Choose your dates"
            />
          </View>
        </View>
      </View>

      <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
        {/* Table */}
        <DynamicTable
          // isLoading={loadingStatus}
          tableHeaders={[
            "Name",
            "Date",
            "Amount",
            "Description",
            "Mode of payment",
          ]}
          tableData={expenseReportList?.table?.data?.map((expense: any) => [
            <Link
              target="_blank"
              to={`${REPORT + EXPENSES_TABLE_URL + EXPENSES_DETAILS_URL}/${
                expense.id
              }`}
            >
              {expense.expense_name}
            </Link>,
            dayjs(expense.date).format(DATE_FORMAT),
            expense.amount,
            expense.description,
            expense.mode_of_payment,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(val) =>
                  setSearchParams({
                    ...Object.fromEntries(searchParams),
                    search: val,
                    currentPage: "1",
                  })
                }
              />
            ),
            filter: (
              <Filter
                onResetFilter={() => {
                  setFilterData(null);
                }}
                title="Expense Filter"
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <SingleSelector
                      required={true}
                      id="mode_of_payment"
                      name="mode_of_payment"
                      label="Mode of Payment"
                      options={amountTypeData?.map((item: any) => ({
                        value: item.amount_for,
                        label: item.amount_for,
                      }))}
                      placeholder="Select Mode of Payment"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="date" type="date" placeholder="Date" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="amount" placeholder="Amount" type="number" />
                  </View>,
                ]}
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={expenseReportList?.table?.current_page}
                last_page={expenseReportList?.table?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};

export default Expense;
