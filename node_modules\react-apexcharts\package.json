{"name": "react-apexcharts", "version": "1.7.0", "description": "React.js wrapper for ApexCharts", "main": "dist/react-apexcharts.min.js", "types": "types/react-apexcharts.d.ts", "scripts": {"build": "concurrently \"rollup -c rollup.config.js\" \"gulp\"", "dev-build": "concurrently \"rollup -c rollup.config.js\" \"gulp devBuild\"", "test": "jest"}, "keywords": ["react-charts", "react", "charts", "graphs", "apexcharts", "data-visualization"], "author": {"name": "Juned <PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/apexcharts/react-apexcharts/issues"}, "license": "MIT", "dependencies": {"prop-types": "^15.8.1"}, "peerDependencies": {"apexcharts": ">=4.0.0", "react": ">=0.13"}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.25.8", "@babel/preset-react": "^7.25.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^18.3.11", "concurrently": "^9.0.1", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "gulp": "^5.0.0", "gulp-babel": "^8.0.0", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-test-renderer": "^18.3.1", "rollup": "4.24.0", "rollup-plugin-postcss": "^4.0.2"}}