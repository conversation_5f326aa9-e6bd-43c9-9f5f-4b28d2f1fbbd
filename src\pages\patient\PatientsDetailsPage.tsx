import View from "@/components/view";
import Text from "@/components/text";
import <PERSON><PERSON> from "@/components/button";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { usePatient } from "@/actions/calls/patient";
import { toast } from "@/utils/custom-hooks/use-toast";
import {
  DOWNLOAD_PATIENT_FILES_URL,
  DOWNLOAD_ANESTHESIA_FILES_URL,
} from "@/utils/urls/backend";
import { Card, CardContent } from "@/components/ui/card";
import { Download, Calendar, Clock, Stethoscope, Plus, ArrowLeft, FileText, Activity } from "lucide-react";
import { Link } from "react-router-dom";
import {
  APPOINTMENT_FORM_URL,
  CONSULTATION_DETAILS_URL,
  CONSULTATION_TABLE_URL,
  PATIENT_TABLE_URL,
  POST_SURGERY_FOLLOW_UP_URL,
  USER_DETAIL_URL,
  USER_TABLE_URL,
} from "@/utils/urls/frontend";
import { useNavigate } from "react-router-dom";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import dayjs from "dayjs";
import PatientInfo from "./PatientInfo";
import BouncingLoader from "@/components/BouncingLoader";
import TabbedCollapsible from "@/components/TabbedCollapsible";

const PatientDetailsPage = () => {
  const { id } = useParams();
  const { patientDetailHandler, downloadPatientHandler } = usePatient();

  const patient = useSelector(
    (state: RootState) => state?.patient?.patientDetailData
  );


  const navigate = useNavigate();

  const [, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // const [readOnly, setReadOnly] = useState(true);
  // const currentSymbol = useSelector(
  //   (state: RootState) => state.systemSettings.settings.currency_symbol
  // );
  // const { appointmentDetailHandler } = useAppointments();

  // const appointmentDetails = useSelector(
  //   (state: RootState) => state.appointment.appointmentDetailData
  // );

  useEffect(() => {
    if (id) {
      patientDetailHandler(
        id,
        () => {
          setLoading(false);
        },
        [],
        (status) => {
          setIsLoading(
            status === "pending"
              ? true
              : status === "failed"
              ? true
              : status === "success" && false
          );
        }
      );
    }
  }, [id]);

  // useEffect(() => {
  //     if (params.id) {
  //       appointmentDetailHandler(params.id, () => {});
  //     }
  //   }, [params.id]);

  // if (loading || !patient) {
  //   return (
  //     <View className="text-center text-muted py-10">
  //       Loading patient data...
  //     </View>
  //   );
  // }

  const handleDownload = (path: string) => {
    if (id) {
      setIsLoading(true);
      downloadPatientHandler(id, path, async (success: boolean) => {
        if (success) {
          toast({
            title: "Success!",
            description: "Successfully downloaded patient details",
            variant: "success",
          });
          setIsLoading(false);
        } else {
          toast({
            title: "Error",
            description: "Failed to download patient details",
            variant: "destructive",
          });
          setIsLoading(false);
        }
      });
    }
  };

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      <View className="space-y-6">
        {/* Enhanced Header Section */}
        <View className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-xl p-6 mb-6 border border-slate-200 dark:border-slate-700">
          <View className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
            <View className="flex-1">
              <View className="flex items-center gap-3 mb-2">
                <View className="p-2 rounded-lg bg-primary/10 text-primary">
                  <Activity size={24} />
                </View>
                <View>
                  <Text
                    as="h1"
                    weight="font-semibold"
                    className="text-2xl font-bold text-slate-900 dark:text-slate-100"
                  >
                    Patient Details
                  </Text>
                  <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
                    Comprehensive patient information and medical history
                  </Text>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                onClick={() =>
                  navigate(`${PATIENT_TABLE_URL}?currentPage=1`, {
                    state: { refresh: true },
                  })
                }
                className="flex items-center gap-2 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200"
              >
                <ArrowLeft size={16} />
                Back
              </Button>

              <Button
                onClick={() => handleDownload(DOWNLOAD_PATIENT_FILES_URL)}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-500/25 transition-all duration-200"
              >
                <FileText size={16} />
                Patient Files
              </Button>

              <Button
                onClick={() => handleDownload(DOWNLOAD_ANESTHESIA_FILES_URL)}
                className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg shadow-emerald-500/25 transition-all duration-200"
              >
                <Download size={16} />
                Anaesthesia Files
              </Button>

              <Button
                onPress={() => {
                  navigate(`${APPOINTMENT_FORM_URL}?patientId=${patient?.id}`);
                }}
                className="flex items-center gap-2 bg-primary hover:bg-primary-600 text-white shadow-lg shadow-primary/25 transition-all duration-200"
              >
                <Plus size={16} />
                Add Appointment
              </Button>
            </View>
          </View>

          {/* Post Surgery Follow Up Button */}
          <View className="flex justify-end mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
            <Link
              target="_blank"
              to={`${POST_SURGERY_FOLLOW_UP_URL}/${patient?.id}`}
              className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-4 py-2 rounded-lg shadow-lg shadow-purple-500/25 transition-all duration-200 text-sm font-medium"
            >
              <Stethoscope size={16} />
              Manage Post Surgery Follow Up
            </Link>
          </View>
        </View>

        {/* Patient Summary Card */}
        <PatientInfo patient={patient} />
      </View>
      {/* 
       <View className="flex justify-between items-center mt-4">
                          <View>
                            <Text as="h2" weight="font-semibold" className="text-2xl font-bold">
                              Appointments
                            </Text>
                          </View>
                        </View> */}
      <View className="flex justify-between items-center mt-8">
        {patient?.appointments?.length > 0 ? (
          <Text as="h2" weight="font-semibold" className="!text-2xl font-bold">
            Appointments
          </Text>
        ) : (
          ""
        )}
      </View>
      {patient?.appointments?.map((data: any, index: number) => {
        return (
          <View key={data.id} className="space-y-6 py-8 border-b">
            <Card>
              <View className="flex justify-between items-center mx-4 mb-6 pt-6">
                <View className="flex !items-center gap-2">
                  <Text
                    as="h2"
                    className="text-lg font-bold flex items-center gap-2"
                  >
                    <View className="flex items-center">
                      Appointment #
                      <Text as="h2" className="text-muted-foreground">
                        {data?.appointment_number}
                      </Text>
                    </View>
                  </Text>
                  {patient?.consultation && (
                    <>
                      {" -> "}
                      <Text as="span">
                        <Link
                          target="_blank"
                          className="text-primary hover:underline"
                          to={
                            CONSULTATION_TABLE_URL +
                            CONSULTATION_DETAILS_URL +
                            "/" +
                            patient?.consultation[index]?.id
                          }
                        >
                          Consultation ({index + 1})
                        </Link>
                      </Text>
                    </>
                  )}

                  {data?.doctor_name && (
                    <>
                      {" -> "}
                      <Text as="span">
                        <Link
                          target="_blank"
                          className=" flex  text-primary hover:underline"
                          to={
                            USER_TABLE_URL +
                            USER_DETAIL_URL +
                            "/" +
                            data?.doctor_id
                          }
                        >
                          <Stethoscope className="h-5 w-5 text-primary mr-2" />
                          <span>{data?.doctor_name}</span>
                        </Link>
                      </Text>
                    </>
                  )}
                </View>

                <Text
                  as="span"
                  className={`px-3 py-1 rounded-full text-xs   font-medium `}
                  style={getStatusColorScheme(data?.status)}
                >
                  {data?.status}
                </Text>
              </View>

              {/* <AppointmentIndex readOnly appointmentDetails={data} showPatientDetails={false} usingAppointmentCardStyle={false} /> */}
              <View>
                {/* {!readOnly && (
                        <View className="flex justify-between items-center">
                          <View>
                            <Text as="h1" weight="font-semibold" className="text-2xl font-bold">
                              Appointment Details
                            </Text>
                            <Text as="p" className="text-muted-foreground">
                              View and manage appointment information
                            </Text>
                          </View>
                          <View className="flex space-x-2">
                            <Button variant="outline" onPress={() => navigate(-1)}>
                              Back to Home
                            </Button>
                          </View>
                        </View>
                      )} */}

                {/* Appointment Status Card */}
                <Card className="border-none">
                  {/* <CardHeader className="pb-2">
                          <View className="flex justify-between items-center">
                            <Text
                              as="span"
                              className={`px-3 py-1 rounded-full text-xs   font-medium`}
                              style={getStatusColorScheme(data?.status)}
                            >
                              {data?.status}
                            </Text>
                          </View>
                        </CardHeader> */}
                  <CardContent>
                    {/* <View className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <View className="flex items-center text-sm">
                              <Calendar className="h-5 w-5 text-primary mr-2" />
                              <Text as="span">
                                {data?.appointment_date || "N/A"}
                              </Text>
                            </View>
                            <View className="flex items-center text-sm">
                              <Clock className="h-5 w-5 text-primary mr-2" />
                              <Text as="span">
                                {dayjs(
                                  dayjs().format("YYYY-MM-DD") +
                                    " " +
                                    data?.appointment_time,
                                  "YYYY-MM-DD HH:mm:ss"
                                ).format("hh:mm A")}
                              </Text>
                            </View>
                            
                          </View> */}

                    <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <View className="">
                        <View>
                          <Text as="h3" className="text-md font-semibold mb-2">
                            Appointment Date & Time
                          </Text>
                        </View>
                        <View className="flex items-center gap-4">
                          <View className="flex !items-center">
                            <Calendar className="h-5 w-5 text-primary mr-2" />
                            <Text as="span">
                              {data?.appointment_date || "N/A"}
                            </Text>
                            {/* <span>{formatDate(appointment.date)}</span> */}
                          </View>
                          <View className="flex !items-center">
                            <Clock className="h-5 w-5 text-primary mr-2" />
                            <Text as="span">
                              {dayjs(
                                dayjs().format("YYYY-MM-DD") +
                                  " " +
                                  data?.appointment_time,
                                "YYYY-MM-DD HH:mm:ss"
                              ).format("hh:mm A")}
                            </Text>
                          </View>
                        </View>
                        {/* <View className="flex items-center">
                            <MapPin className="h-5 w-5 text-primary mr-2" />
                            <span>{appointment.location}</span>
                          </View> */}
                      </View>

                      <View>
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Appointment Type
                        </Text>
                        <Text as="p" className="text-muted-foreground ">
                          {data?.type || "N/A"}
                        </Text>
                      </View>
                      {/* <View>
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Enrollment Fees
                        </Text>
                        <Text as="p" className="text-muted-foreground ">
                          {patient?.enroll_fees ? currentSymbol : ""}
                          {patient?.enroll_fees || "N/A"}
                        </Text>
                      </View> */}
                    </View>

                    {data?.complaint && (
                      <View className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Complaints
                        </Text>
                        <Text as="p" className="text-sm">
                          {data?.complaint || "N/A"}
                        </Text>
                      </View>
                    )}

                   <View className="mt-6">
                            <Text as="h3" className="text-md font-semibold mb-2">
                              Consultation Details
                            </Text>
                          </View>
                    {
                      patient?.consultation.length > 0 ? (
                        <>
                          
                           {
                          patient?.consultation.map((consultation: any) => 
                             {
                              const departmentType = consultation?.type === "Proctology" ? "proctology" : consultation?.type === "Non Proctology" ? "non_proctology" : "allopathy";
                              return (
                               data?.id === consultation?.appointment_id && (
                                //  <View key={consultation.id} className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
                                   
                                //  </View>
                                <View key={consultation.id} className="mt-4">
                                   <TabbedCollapsible 
                                   tabs={[
                                      {
                                        title: "Chief Complaints",
                                        items: consultation[departmentType]?.chief_complaints
                                          ? JSON.parse(
                                              consultation[departmentType]?.chief_complaints
                                            ).map((item: any) => item.label)
                                          : ["N/A"],
                                        badge: consultation[departmentType]?.chief_complaints
                                          ? JSON.parse(
                                              consultation[departmentType]?.chief_complaints
                                            ).length.toString()
                                          : "0",
                                      },
                                      {
                                        title: "On Examination",
                                        items: consultation[departmentType]?.on_examination
                                          ? JSON.parse(
                                              consultation[departmentType]?.on_examination
                                            ).map((item: any) => item.label)
                                          : ["N/A"],
                                        badge: consultation[departmentType]?.on_examination
                                          ? JSON.parse(
                                              consultation[departmentType]?.on_examination
                                            ).length.toString()
                                          : "0",
                                      }
                                    ]}
                                  />

                                
                                </View>
                                 )
                               )
                             }
                          )
                        }
                        </>
                      ) : (
                        <View className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
                          <Text as="h5" className="text-md font-semibold">
                            No Consultation Details Found
                          </Text>
                        </View>
                      )
                        
                    }
                  </CardContent>
                </Card>

                {/* Provider Information */}

                {/* <Card className={!readOnly ? "" : "border-none"}>
                        <CardHeader>
                          <CardTitle className="text-lg">Doctor Summary</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <View className="flex items-center mb-4">
                            <View className="w-16 h-16 rounded-full bg-accent-50 flex items-center justify-center text-accent text-xl font-bold mr-4">
                              {appointmentDetails?.doctor_name?.split(" ")[0][0]}
                            </View>
                            <View>
                              <Text as="h3" className="font-semibold text-xl">
                                <Link
                                  className="text-accent hover:underline"
                                  to={
                                    USER_TABLE_URL +
                                    USER_DETAIL_URL +
                                    "/" +
                                    appointmentDetails?.doctor_id
                                  }
                                >
                                  {appointmentDetails?.doctor_name}
                                </Link>
                              </Text>
                            </View>
                          </View>
              
                          <View className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <View className="flex items-center">
                              <Phone className="h-5 w-5 text-muted-foreground mr-2" />
                              <Text as="span">
                                {appointmentDetails?.doctor_phone || "N/A"}
                              </Text>
                            </View>
                            <View className="flex items-center">
                              <Mail className="h-5 w-5 text-muted-foreground mr-2" />
                              <Text as="span">
                                {appointmentDetails?.doctor_email || "N/A"}
                              </Text>
                            </View>
                          </View>
                        </CardContent>
                      </Card> */}
              </View>
            </Card>
          </View>
        );
      })}
    </React.Fragment>
  );
};

export default PatientDetailsPage;
