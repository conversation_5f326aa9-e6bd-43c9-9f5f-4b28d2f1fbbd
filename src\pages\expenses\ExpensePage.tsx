import { useExpenses } from "@/actions/calls/expenses";
import BouncingLoader from "@/components/BouncingLoader";
import Button from "@/components/button";
import ActionMenu from "@/components/editDeleteAction";
import Modal from "@/components/Modal";
import PaginationComponent from "@/components/Pagination";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import { handleSortChange } from "@/utils/helperFunctions";
import {
  DATE_FORMAT,
  EXPENSES_DETAILS_URL,
  EXPENSES_EDIT_URL,
  EXPENSES_FORM_URL,
  EXPENSES_TABLE_URL,
} from "@/utils/urls/frontend";
import dayjs from "dayjs";
import { Plus, DollarSign, TrendingUp, Calendar, FileText } from "lucide-react";
import InfoCard from "@/components/ui/infoCard";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useNavigate, useSearchParams } from "react-router-dom";

const ExpensesPage = () => {
  const navigate = useNavigate();
  const { expensesList, deleteExpenses, cleanUp } = useExpenses();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const [isLoading, setIsLoading] = useState(false);
  const expensesListData = useSelector(
    (state: any) => state?.expenses?.expensesList
  );
  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      expensesList(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        [],
        (status) => {
          setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
        }
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
    searchParams?.get("currentPage"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const handleDeleteExpenses = () => {
    if (deleteId) {
      deleteExpenses(deleteId, (success: boolean) => {
        if (success) {
          modalCloseHandler();
          expensesList(searchParams?.get("currentPage") ?? 1, () => {});
        }
      });
    }
  };

  const sortOptions: SortOption[] = [
    { label: "Date (A-Z)", value: "expenses_date", order: "asc" },
    { label: "Date (Z-A)", value: "expenses_date", order: "desc" },
    { label: "Amount (Low to High)", value: "amount", order: "asc" },
    { label: "Amount (High to Low)", value: "amount", order: "desc" },
    { label: "Expense Name (A-Z)", value: "expense_name", order: "asc" },
    { label: "Expense Name (Z-A)", value: "expense_name", order: "desc" },
    { label: "Mode of Payment (A-Z)", value: "mode_of_payment", order: "asc" },
    { label: "Mode of Payment (Z-A)", value: "mode_of_payment", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <>

      <React.Fragment>
        <BouncingLoader isLoading={isLoading} />
        <Modal
          title="Expense Delete"
          isOpen={deleteId ? true : false}
          onClose={modalCloseHandler}
          description="Are you sure you want to delete this data? This action cannot be undone and will permanently remove the data from the system."
        >
          <View className="flex justify-end gap-2">
            <Button
              variant="outline"
              className="text-black"
              onPress={modalCloseHandler}
            >
              Cancel
            </Button>
            <Button variant="danger" onPress={handleDeleteExpenses}>
              Delete
            </Button>
          </View>
        </Modal>
        {/* Header Section */}
        <View className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-soft dark:shadow-none border border-slate-200 dark:border-slate-700 mb-6">
          <View className="flex items-center gap-3">
            <View className="p-2 rounded-lg bg-primary/10">
              <DollarSign className="h-6 w-6 text-primary" />
            </View>
            <View>
              <Text
                as="h1"
                weight="font-semibold"
                className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-1"
              >
                Expenses
              </Text>
              <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
                Manage and track all hospital expenses
              </Text>
            </View>
          </View>
        </View>

        {/* Stats Cards */}
        <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <InfoCard
            label="Total Expenses"
            value={expensesListData?.total || 0}
            valueStyle="!text-blue-600 dark:!text-blue-400"
            icon={<DollarSign size={20} />}
            iconStyle="!bg-gradient-to-br !from-blue-100 !via-blue-200 !to-blue-300 dark:!from-blue-800/40 dark:!via-blue-700/40 dark:!to-blue-600/40 !text-blue-600 dark:!text-blue-400 !shadow-lg !shadow-blue-500/25 dark:!shadow-blue-400/20"
            className="hover:scale-[1.02] transition-transform duration-200"
          />

          <InfoCard
            label="This Month"
            value={expensesListData?.data?.filter((expense: any) => {
              const currentMonth = new Date().getMonth();
              const expenseMonth = new Date(expense.date).getMonth();
              return expenseMonth === currentMonth;
            }).length || 0}
            valueStyle="!text-emerald-600 dark:!text-emerald-400"
            icon={<Calendar size={20} />}
            iconStyle="!bg-gradient-to-br !from-emerald-100 !via-emerald-200 !to-emerald-300 dark:!from-emerald-800/40 dark:!via-emerald-700/40 dark:!to-emerald-600/40 !text-emerald-600 dark:!text-emerald-400 !shadow-lg !shadow-emerald-500/25 dark:!shadow-emerald-400/20"
            className="hover:scale-[1.02] transition-transform duration-200"
          />

          <InfoCard
            label="Total Amount"
            value={`₹${expensesListData?.data?.reduce((sum: number, expense: any) => sum + (parseFloat(expense.amount) || 0), 0).toLocaleString() || '0'}`}
            valueStyle="!text-purple-600 dark:!text-purple-400"
            icon={<TrendingUp size={20} />}
            iconStyle="!bg-gradient-to-br !from-purple-100 !via-purple-200 !to-purple-300 dark:!from-purple-800/40 dark:!via-purple-700/40 dark:!to-purple-600/40 !text-purple-600 dark:!text-purple-400 !shadow-lg !shadow-purple-500/25 dark:!shadow-purple-400/20"
            className="hover:scale-[1.02] transition-transform duration-200"
          />

          <InfoCard
            label="Average Amount"
            value={`₹${expensesListData?.data?.length > 0 ?
              (expensesListData.data.reduce((sum: number, expense: any) => sum + (parseFloat(expense.amount) || 0), 0) / expensesListData.data.length).toFixed(0) :
              '0'}`}
            valueStyle="!text-orange-600 dark:!text-orange-400"
            icon={<FileText size={20} />}
            iconStyle="!bg-gradient-to-br !from-orange-100 !via-orange-200 !to-orange-300 dark:!from-orange-800/40 dark:!via-orange-700/40 dark:!to-orange-600/40 !text-orange-600 dark:!text-orange-400 !shadow-lg !shadow-orange-500/25 dark:!shadow-orange-400/20"
            className="hover:scale-[1.02] transition-transform duration-200"
          />
        </View>

        <Card className="overflow-hidden border-0 shadow-medium bg-white dark:bg-slate-800">
          <DynamicTable
            tableHeaders={[
              "Expense Name",
              "Date",
              "Amount",
              "Mode of Payment",
              "Action",
            ]}
            tableData={expensesListData?.data?.map((data: any) => [
              <Link
                to={`${EXPENSES_TABLE_URL + EXPENSES_DETAILS_URL}/${data.id}`}
              >
                {data?.expense_name}
              </Link>,
              data?.date? dayjs(data?.date).format(DATE_FORMAT) : "-", ,
              data?.amount,
              data?.mode_of_payment,
              <ActionMenu
                onEdit={() =>
                  navigate(
                    `${EXPENSES_TABLE_URL + EXPENSES_EDIT_URL}/${data.id}`
                  )
                }
                onDelete={() => setDeleteId(data.id)}
              />,
            ])}
            header={{
              search: (
                <SearchBar
                  onSearch={(val) =>
                    setSearchParams({
                      ...Object.fromEntries(searchParams),
                      search: val,
                      currentPage: "1",
                    })
                  }
                />
              ),
              sort: (
                <DataSort
                  sortOptions={sortOptions}
                  onSort={(option) =>
                    handleSortChange(
                      option,
                      setActiveSort,
                      setSearchParams,
                      searchParams
                    )
                  }
                  activeSort={activeSort ?? undefined}
                />
              ),
              action: (
                <Button
                  variant="primary"
                  size="small"
                  className="flex items-center gap-2"
                  onPress={() =>
                    navigate(EXPENSES_TABLE_URL + EXPENSES_FORM_URL)
                  }
                >
                  <Plus size={16} /> Add Expense
                </Button>
              ),
            }}
            footer={{
              pagination: (
                <PaginationComponent
                  current_page={expensesListData?.current_page}
                  last_page={expensesListData?.last_page}
                  getPageNumberHandler={(page) =>
                    setSearchParams(
                      {
                        ...Object.fromEntries(searchParams),
                        currentPage: `${page}`,
                      },
                      { replace: true }
                    )
                  }
                />
              ),
            }}
          />
        </Card>
      </React.Fragment>
    </>
  );
};
export default ExpensesPage;
