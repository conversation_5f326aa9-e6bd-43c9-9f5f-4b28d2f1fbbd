{"name": "@svgdotjs/svg.filter.js", "version": "3.0.9", "description": "A plugin for svg.js adding filter functionality", "keywords": ["svg.js", "filter", "effect"], "bugs": "https://github.com/svgdotjs/svg.filter.js/issues", "license": "MIT", "typings": "./svg.filter.js.d.ts", "author": {"name": "Wout <PERSON>"}, "maintainers": [{"name": "Wout <PERSON>", "email": "<EMAIL>", "web": "https://svgdotjs.github.io/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/svgdotjs/svg.filter.js", "type": "module", "main": "dist/svg.filter.node.cjs", "unpkg": "dist/svg.filter.min.js", "jsdelivr": "dist/svg.filter.min.js", "browser": "src/svg.filter.js", "module": "src/svg.filter.js", "exports": {".": {"import": {"types": "./svg.filter.js.d.ts", "default": "./src/svg.filter.js"}, "require": {"types": "./svg.filter.js.d.cts", "default": "./dist/svg.filter.node.cjs"}}}, "files": ["/dist", "/src", "/svg.filter.js.d.ts", "/svg.filter.js.d.cts"], "repository": {"type": "git", "url": "https://github.com/svgdotjs/svg.filter.js.git"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"build": "npm run fix && npm run rollup", "fix": "npx eslint ./src --fix", "lint": "npx eslint ./src", "rollup": "npx rollup -c .config/rollup.config.js", "zip": "zip -j dist/svg.filter.js.zip -- LICENSE README.md dist/svg.filter.js dist/svg.filter.js.map dist/svg.filter.min.js dist/svg.filter.min.js.map", "prepublishOnly": "rm -rf ./dist && npm run build", "postpublish": "npm run zip"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-multi-entry": "^6.0.1", "@rollup/plugin-node-resolve": "^16.0.0", "babel-eslint": "^10.1.0", "core-js": "^3.40.0", "eslint": "^8.0.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.9", "eslint-plugin-standard": "^5.0.0", "jasmine": "^5.6.0", "jasmine-core": "^5.6.0", "rollup": "^4.34.8", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-terser": "^7.0.2", "babel-plugin-polyfill-corejs3": "^0.11.1"}, "dependencies": {"@svgdotjs/svg.js": "^3.2.4"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"]}