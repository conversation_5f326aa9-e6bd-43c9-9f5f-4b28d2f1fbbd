import { useEffect, useState } from "react";
import DashboardLayout from "@/components/DashboardLayout";
// import { Card } from "@/components/ui/card";
import {
  UserPlus,
  CalendarCheck,
  Bed,
  Stethoscope,
  Users,
  // ArrowUp,
  // ArrowDown,
  // TrendingUp,
} from "lucide-react";
import { useSelector } from "react-redux";
// import { logoutSlice } from "@/actions/slices/auth";

import { useDashboard } from "@/actions/calls/dashboard";
import View from "@/components/view";
import Text from "@/components/text";
import InfoCard from "@/components/ui/infoCard";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import { useSearchParams } from "react-router-dom";
import PaginationComponent from "@/components/Pagination";
import Input from "@/components/input";
import Select from "@/components/Select";
import DataSort, { SortOption } from "@/components/SortData";
import { handleSortChange } from "@/utils/helperFunctions";
import Filter from "../filter";
import SearchBar from "@/components/ui/search-bar";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import dayjs from "dayjs";
import {
  CONSULTATION_DETAILS_URL,
  CONSULTATION_TABLE_URL,
  DATE_FORMAT,
} from "@/utils/urls/frontend";
import { Link } from "react-router-dom";
import BouncingLoader from "@/components/BouncingLoader";




// const StatCard = ({
//   title,
//   value,
//   icon,
//   trend,
//   trendValue,
// }: {
//   title: string;
//   value: string;
//   icon?: React.ReactNode;
//   trend?: "up" | "down" | "neutral";
//   trendValue?: string;
// }) => {

//   useEffect(() => {
//     // dispatch(logoutSlice());
//   }, []);

//   return (
//     <Card className="p-6 bg-card hover:shadow-lg transition-shadow duration-200">
//       <View className="flex justify-between items-start">
//         <View>
//           <Text as="p" className="text-text-light text-sm font-medium mb-1">{title}</Text>
//           <Text as="h3" className="text-2xl font-bold text-text-DEFAULT">{value}</Text>
//         </View>
//         <View className="p-3 rounded-full bg-primary-50 text-primary-500">
//           {icon}
//         </View>
//       </View>
//       {
//         trend && trendValue && (
//           <View className="mt-4 flex items-center">
//         <Text as="span"
//           className={`text-sm font-medium flex items-center ${
//             trend === "up"
//               ? "text-accent"
//               : trend === "down"
//               ? "text-danger"
//               : "text-text-light"
//           }`}
//         >
//           {trend === "up" ? (
//             <ArrowUp size={16} className="mr-1" />
//           ) : trend === "down" ? (
//             <ArrowDown size={16} className="mr-1" />
//           ) : (
//             <TrendingUp size={16} className="mr-1" />
//           )}
//           {trendValue}
//         </Text>
//         <Text as="span" className="text-sm text-text-lighter ml-1">vs last month</Text>
//       </View>
//         )
//       }
//     </Card>
//   );
// };

// const RecentAppointmentsCard = () => {
//   const appointments = [
//     {
//       id: 1,
//       patient: "John Doe",
//       doctor: "Dr. Sarah Smith",
//       time: "09:00 AM",
//       status: "Completed",
//     },
//     {
//       id: 2,
//       patient: "Jane Smith",
//       doctor: "Dr. Robert Johnson",
//       time: "10:30 AM",
//       status: "Scheduled",
//     },
//     {
//       id: 3,
//       patient: "Michael Brown",
//       doctor: "Dr. Lisa Anderson",
//       time: "11:45 AM",
//       status: "Cancelled",
//     },
//     {
//       id: 4,
//       patient: "Emily Davis",
//       doctor: "Dr. James Wilson",
//       time: "02:15 PM",
//       status: "Scheduled",
//     },
//   ];

//   return (
//     <Card className="p-6">
//       <div className="flex justify-between items-center mb-6">
//         <h3 className="text-lg font-bold text-text-DEFAULT">
//           Recent Appointments
//         </h3>
//         <a
//           href="#"
//           className="text-sm text-primary hover:text-primary-600 font-medium"
//         >
//           View all
//         </a>
//       </div>
//       <div className="overflow-x-auto">
//         <table className="w-full">
//           <thead>
//             <tr className="border-b border-neutral-200">
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Patient
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Doctor
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Time
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Status
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             {appointments.map((appointment) => (
//               <tr
//                 key={appointment.id}
//                 className="border-b border-neutral-200 hover:bg-neutral-50"
//               >
//                 <td className="py-3 px-4 text-sm">{appointment.patient}</td>
//                 <td className="py-3 px-4 text-sm">{appointment.doctor}</td>
//                 <td className="py-3 px-4 text-sm">{appointment.time}</td>
//                 <td className="py-3 px-4 text-sm">
//                   <span
//                     className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
//                       appointment.status === "Completed"
//                         ? "bg-accent-50 text-accent"
//                         : appointment.status === "Scheduled"
//                         ? "bg-primary-50 text-primary"
//                         : "bg-neutral-100 text-text-light"
//                     }`}
//                   >
//                     {appointment.status}
//                   </span>
//                 </td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//     </Card>
//   );
// };

const Dashboard = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { getDashboardDataHandler , cleanUp} = useDashboard();
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const [isLoading,setIsLoading] = useState(true);

  

  // console.log(checkLoadinStatus.isLoading, "isLoading");
  
 
  // const consultationListData = useSelector(
  //   (state: any) => state.consultation.consultationListData
  // );
  const dashboardData = useSelector(
    (state: any) => state?.dashboard?.dashboardData
  );

  // useEffect(() => {
  //   getDashboardDataHandler(() => {});
  // }, []);

  useEffect(() => {
    getDashboardDataHandler(
      searchParams?.get("currentPage") ?? 1,
      () => {},
      searchParams.get("search") ?? null,
      searchParams.get("sort_by") ?? null,
      searchParams.get("sort_order") ?? null,
      filterData,
      (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
    );
    return () => {
      cleanUp();
    };
  }, [
    filterData,
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const sortOptions: SortOption[] = [
    { label: "Patient ID (A-Z)", value: "patient_id", order: "asc" },
    { label: "Patient ID (Z-A)", value: "patient_id", order: "desc" },
    { label: "Patient Name (A-Z)", value: "patient_name", order: "asc" },
    { label: "Patient Name (Z-A)", value: "patient_name", order: "desc" },
    { label: "Next Visit Date (A-Z)", value: "next_visit_date", order: "asc" },
    { label: "Next Visit Date (Z-A)", value: "next_visit_date", order: "desc" },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
    { label: "Payment Status (A-Z)", value: "payment_status", order: "asc" },
    { label: "Payment Status (Z-A)", value: "payment_status", order: "desc" },
  ];
  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <DashboardLayout>
       <View className="fixed top-4 left-0  w-full z-50">
        <BouncingLoader  isLoading={isLoading} />
      </View>
      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-slate-900 dark:text-white mb-1"
        >
          Dashboard
        </Text>
        <Text as="p" className="text-slate-600 dark:text-slate-400 text-sm">
          Welcome back to {import.meta.env.VITE_HOSPITAL_NAME} Hospital Management System
        </Text>
      </View>

      {/* Stats Grid */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
        <InfoCard
          label="Total Patients"
          value={dashboardData?.totalPatients ?? "N/A"}
          valueStyle="!text-emerald-600 dark:!text-emerald-400"
          icon={<UserPlus size={20} />}
          iconStyle="!bg-emerald-100 dark:!bg-emerald-900/30 !text-emerald-600 dark:!text-emerald-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Total Appointments"
          value={dashboardData?.totalAppointments ?? "N/A"}
          valueStyle="!text-blue-600 dark:!text-blue-400"
          icon={<CalendarCheck size={20} />}
          iconStyle="!bg-blue-100 dark:!bg-blue-900/30 !text-blue-600 dark:!text-blue-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Total OPD Cases"
          value={dashboardData?.totalOPD ?? "N/A"}
          valueStyle="!text-purple-600 dark:!text-purple-400"
          icon={<Stethoscope size={20} />}
          iconStyle="!bg-purple-100 dark:!bg-purple-900/30 !text-purple-600 dark:!text-purple-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Total IPD Cases"
          value={dashboardData?.totalIPD ?? "N/A"}
          valueStyle="!text-orange-600 dark:!text-orange-400"
          icon={<Bed size={20} />}
          iconStyle="!bg-orange-100 dark:!bg-orange-900/30 !text-orange-600 dark:!text-orange-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Beds Occupied"
          value={dashboardData?.noOfBedsOccupied ?? "N/A"}
          valueStyle="!text-red-600 dark:!text-red-400"
          icon={<Bed size={20} />}
          iconStyle="!bg-red-100 dark:!bg-red-900/30 !text-red-600 dark:!text-red-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />

        <InfoCard
          label="Total Users"
          value={dashboardData?.totalUsers ?? "N/A"}
          valueStyle="!text-indigo-600 dark:!text-indigo-400"
          icon={<Users size={20} />}
          iconStyle="!bg-indigo-100 dark:!bg-indigo-900/30 !text-indigo-600 dark:!text-indigo-400"
          className="hover:scale-[1.02] transition-transform duration-200"
        />
      </View>
      <View className="mb-6">
        <Text
          as="h2"
          weight="font-semibold"
          className="text-2xl font-bold text-slate-900 dark:text-white mb-2"
        >
          Upcoming Consultations
        </Text>
        <Text as="p" className="text-slate-600 dark:text-slate-400">
          Recent consultation appointments and their status
        </Text>
      </View>
      <Card className="w-full mb-8 overflow-hidden border-0 shadow-medium">
        <DynamicTable
          tableHeaders={[
            "Patient Number",
            "Patient Name",
            "Next Visit Date",
            "Status",
            "Payment Status",
            // "Actions",
          ]}
          tableData={dashboardData?.consultation?.data?.map((data: any) => [
            <Link
              to={
                CONSULTATION_TABLE_URL +
                CONSULTATION_DETAILS_URL +
                "/" +
                data.id
              }
              className="font-medium text-text-DEFAULT hover:text-primary hover:underline"
            >
              {data?.patient_number}
            </Link>,
            data?.patient_name,
            data?.next_visit_date
              ? dayjs(data?.next_visit_date).format(DATE_FORMAT)
              : "NA",
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.status)}
            >
              {data?.status || "N/A"}
            </Text>,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.payment_status)}
            >
              {data?.payment_status || "N/A"}
            </Text>,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                      search: value,
                    },
                    { replace: true }
                  );
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            filter: (
              <Filter
                title="Consultation Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                    },
                    { replace: true }
                  );
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_number" placeholder="Patient Number" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="patient_name" placeholder="Patient Name" />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input name="referred_by_name" placeholder="Referred By" />
                  // </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     name="appointment_number"
                  //     placeholder="Appointment Number"
                  //   />
                  // </View>,
                  // <View className="w-full my-4">
                  //   <Input name="doctor_name" placeholder="Doctor Name" />
                  // </View>,
                  <View className="w-full my-4">
                    <Select
                      name="status"
                      placeholder="Select Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Select
                      name="payment_status"
                      placeholder="Select Payment Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     type="date"
                  //     name="next_visit_date"
                  //     placeholder="Next Visit Date"
                  //     onFocus={(e) => (e.target.type = "date")}
                  //   />
                  // </View>,
                ]}
              />
            ),
          }}
          footer={{
            pagination: (
             <PaginationComponent
                current_page={dashboardData?.consultation?.current_page}
                last_page={dashboardData?.consultation?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>

      {/* Recent Appointments */}
      {/* <div className="mb-6">
        <RecentAppointmentsCard />
      </div> */}

      {/* Additional Dashboard Content */}
      {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-bold text-text-DEFAULT mb-4">Hospital Overview</h3>
          <div className="h-64 flex items-center justify-center bg-neutral-50 rounded-lg">
            <p className="text-text-light">Chart will be displayed here</p>
          </div>
        </Card>
        <Card className="p-6">
          <h3 className="text-lg font-bold text-text-DEFAULT mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex items-start gap-4 pb-4 border-b border-neutral-200 last:border-0">
                <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0">
                  <span className="text-xs font-medium text-primary-600">
                    {["JD", "AS", "TB", "MP"][index]}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium">
                    {[
                      "Dr. John added a new patient record",
                      "Appointment rescheduled for Jane Smith",
                      "New prescription created for Tom Brown",
                      "Medical report uploaded for Mary Parker"
                    ][index]}
                  </p>
                  <p className="text-xs text-text-lighter mt-1">
                    {["2 hours ago", "4 hours ago", "Yesterday", "2 days ago"][index]}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div> */}
    </DashboardLayout>
  );
};

export default Dashboard;
