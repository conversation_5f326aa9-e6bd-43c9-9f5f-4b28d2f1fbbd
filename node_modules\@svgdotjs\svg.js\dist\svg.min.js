/*! @svgdotjs/svg.js v3.2.4 MIT*/;
/*!
* @svgdotjs/svg.js - A lightweight library for manipulating and animating SVG.
* @version 3.2.4
* https://svgjs.dev/
*
* @copyright Wout Fierens <<EMAIL>>
* @license MIT
*
* BUILT: Thu Jun 27 2024 12:00:16 GMT+0200 (Central European Summer Time)
*/
var SVG=function(){"use strict";const t={},e=[];function n(e,i){if(Array.isArray(e))for(const t of e)n(t,i);else if("object"!=typeof e)r(Object.getOwnPropertyNames(i)),t[e]=Object.assign(t[e]||{},i);else for(const t in e)n(t,e[t])}function i(e){return t[e]||{}}function r(t){e.push(...t)}function s(t,e){let n;const i=t.length,r=[];for(n=0;n<i;n++)r.push(e(t[n]));return r}function o(t,e){let n;const i=t.length,r=[];for(n=0;n<i;n++)e(t[n])&&r.push(t[n]);return r}function h(t){return t%360*Math.PI/180}function u(t){return t.replace(/([A-Z])/g,(function(t,e){return"-"+e.toLowerCase()}))}function a(t){return t.charAt(0).toUpperCase()+t.slice(1)}function l(t,e,n,i){return null!=e&&null!=n||(i=i||t.bbox(),null==e?e=i.width/i.height*n:null==n&&(n=i.height/i.width*e)),{width:e,height:n}}function c(t,e){const n=t.origin;let i=null!=t.ox?t.ox:null!=t.originX?t.originX:"center",r=null!=t.oy?t.oy:null!=t.originY?t.originY:"center";null!=n&&([i,r]=Array.isArray(n)?n:"object"==typeof n?[n.x,n.y]:[n,n]);const s="string"==typeof i,o="string"==typeof r;if(s||o){const{height:t,width:n,x:h,y:u}=e.bbox();s&&(i=i.includes("left")?h:i.includes("right")?h+n:h+n/2),o&&(r=r.includes("top")?u:r.includes("bottom")?u+t:u+t/2)}return[i,r]}const f=new Set(["desc","metadata","title"]),d=t=>f.has(t.nodeName),m=(t,e,n={})=>{const i={...e};for(const t in i)i[t].valueOf()===n[t]&&delete i[t];Object.keys(i).length?t.node.setAttribute("data-svgjs",JSON.stringify(i)):(t.node.removeAttribute("data-svgjs"),t.node.removeAttribute("svgjs:data"))};var p={__proto__:null,capitalize:a,degrees:function(t){return 180*t/Math.PI%360},filter:o,getOrigin:c,isDescriptive:d,map:s,proportionalSize:l,radians:h,unCamelCase:u,writeDataToDom:m};const y="http://www.w3.org/2000/svg",w="http://www.w3.org/1999/xhtml",g="http://www.w3.org/2000/xmlns/",_="http://www.w3.org/1999/xlink";var x={__proto__:null,html:w,svg:y,xlink:_,xmlns:g};const b={window:"undefined"==typeof window?null:window,document:"undefined"==typeof document?null:document};function v(t=null,e=null){b.window=t,b.document=e}const M={};function O(){M.window=b.window,M.document=b.document}function k(){b.window=M.window,b.document=M.document}function T(){return b.window}class C{}const N={},S="___SYMBOL___ROOT___";function E(t,e=y){return b.document.createElementNS(e,t)}function j(t,e=!1){if(t instanceof C)return t;if("object"==typeof t)return z(t);if(null==t)return new N[S];if("string"==typeof t&&"<"!==t.charAt(0))return z(b.document.querySelector(t));const n=e?b.document.createElement("div"):E("svg");return n.innerHTML=t,t=z(n.firstChild),n.removeChild(n.firstChild),t}function D(t,e){return e&&(e instanceof b.window.Node||e.ownerDocument&&e instanceof e.ownerDocument.defaultView.Node)?e:E(t)}function I(t){if(!t)return null;if(t.instance instanceof C)return t.instance;if("#document-fragment"===t.nodeName)return new N.Fragment(t);let e=a(t.nodeName||"Dom");return"LinearGradient"===e||"RadialGradient"===e?e="Gradient":N[e]||(e="Dom"),new N[e](t)}let z=I;function P(t,e=t.name,n=!1){return N[e]=t,n&&(N[S]=t),r(Object.getOwnPropertyNames(t.prototype)),t}function R(t){return N[t]}let L=1e3;function q(t){return"Svgjs"+a(t)+L++}function F(t){for(let e=t.children.length-1;e>=0;e--)F(t.children[e]);return t.id?(t.id=q(t.nodeName),t):t}function X(t,e){let n,i;for(i=(t=Array.isArray(t)?t:[t]).length-1;i>=0;i--)for(n in e)t[i].prototype[n]=e[n]}function Y(t){return function(...e){const n=e[e.length-1];return!n||n.constructor!==Object||n instanceof Array?t.apply(this,e):t.apply(this,e.slice(0,-1)).attr(n)}}n("Dom",{siblings:function(){return this.parent().children()},position:function(){return this.parent().index(this)},next:function(){return this.siblings()[this.position()+1]},prev:function(){return this.siblings()[this.position()-1]},forward:function(){const t=this.position();return this.parent().add(this.remove(),t+1),this},backward:function(){const t=this.position();return this.parent().add(this.remove(),t?t-1:0),this},front:function(){return this.parent().add(this.remove()),this},back:function(){return this.parent().add(this.remove(),0),this},before:function(t){(t=j(t)).remove();const e=this.position();return this.parent().add(t,e),this},after:function(t){(t=j(t)).remove();const e=this.position();return this.parent().add(t,e+1),this},insertBefore:function(t){return(t=j(t)).before(this),this},insertAfter:function(t){return(t=j(t)).after(this),this}});const B=/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,H=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,V=/rgb\((\d+),(\d+),(\d+)\)/,$=/(#[a-z_][a-z0-9\-_]*)/i,U=/\)\s*,?\s*/,W=/\s/g,Q=/^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i,J=/^rgb\(/,Z=/^(\s+)?$/,K=/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,tt=/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,et=/[\s,]+/,nt=/[MLHVCSQTAZ]/i;var it={__proto__:null,delimiter:et,hex:H,isBlank:Z,isHex:Q,isImage:tt,isNumber:K,isPathLetter:nt,isRgb:J,numberAndUnit:B,reference:$,rgb:V,transforms:U,whitespace:W};function rt(t){const e=Math.round(t),n=Math.max(0,Math.min(255,e)).toString(16);return 1===n.length?"0"+n:n}function st(t,e){for(let n=e.length;n--;)if(null==t[e[n]])return!1;return!0}function ot(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}n("Dom",{classes:function(){const t=this.attr("class");return null==t?[]:t.trim().split(et)},hasClass:function(t){return-1!==this.classes().indexOf(t)},addClass:function(t){if(!this.hasClass(t)){const e=this.classes();e.push(t),this.attr("class",e.join(" "))}return this},removeClass:function(t){return this.hasClass(t)&&this.attr("class",this.classes().filter((function(e){return e!==t})).join(" ")),this},toggleClass:function(t){return this.hasClass(t)?this.removeClass(t):this.addClass(t)}}),n("Dom",{css:function(t,e){const n={};if(0===arguments.length)return this.node.style.cssText.split(/\s*;\s*/).filter((function(t){return!!t.length})).forEach((function(t){const e=t.split(/\s*:\s*/);n[e[0]]=e[1]})),n;if(arguments.length<2){if(Array.isArray(t)){for(const e of t){const t=e;n[e]=this.node.style.getPropertyValue(t)}return n}if("string"==typeof t)return this.node.style.getPropertyValue(t);if("object"==typeof t)for(const e in t)this.node.style.setProperty(e,null==t[e]||Z.test(t[e])?"":t[e])}return 2===arguments.length&&this.node.style.setProperty(t,null==e||Z.test(e)?"":e),this},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},visible:function(){return"none"!==this.css("display")}}),n("Dom",{data:function(t,e,n){if(null==t)return this.data(s(o(this.node.attributes,(t=>0===t.nodeName.indexOf("data-"))),(t=>t.nodeName.slice(5))));if(t instanceof Array){const e={};for(const n of t)e[n]=this.data(n);return e}if("object"==typeof t)for(e in t)this.data(e,t[e]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+t))}catch(e){return this.attr("data-"+t)}else this.attr("data-"+t,null===e?null:!0===n||"string"==typeof e||"number"==typeof e?e:JSON.stringify(e));return this}}),n("Dom",{remember:function(t,e){if("object"==typeof arguments[0])for(const e in t)this.remember(e,t[e]);else{if(1===arguments.length)return this.memory()[t];this.memory()[t]=e}return this},forget:function(){if(0===arguments.length)this._memory={};else for(let t=arguments.length-1;t>=0;t--)delete this.memory()[arguments[t]];return this},memory:function(){return this._memory=this._memory||{}}});class ht{constructor(...t){this.init(...t)}static isColor(t){return t&&(t instanceof ht||this.isRgb(t)||this.test(t))}static isRgb(t){return t&&"number"==typeof t.r&&"number"==typeof t.g&&"number"==typeof t.b}static random(t="vibrant",e){const{random:n,round:i,sin:r,PI:s}=Math;if("vibrant"===t){const t=24*n()+57,e=38*n()+45,i=360*n();return new ht(t,e,i,"lch")}if("sine"===t){const t=i(80*r(2*s*(e=null==e?n():e)/.5+.01)+150),o=i(50*r(2*s*e/.5****)+200),h=i(100*r(2*s*e/.5****)+150);return new ht(t,o,h)}if("pastel"===t){const t=8*n()+86,e=17*n()+9,i=360*n();return new ht(t,e,i,"lch")}if("dark"===t){const t=10+10*n(),e=50*n()+86,i=360*n();return new ht(t,e,i,"lch")}if("rgb"===t){const t=255*n(),e=255*n(),i=255*n();return new ht(t,e,i)}if("lab"===t){const t=100*n(),e=256*n()-128,i=256*n()-128;return new ht(t,e,i,"lab")}if("grey"===t){const t=255*n();return new ht(t,t,t)}throw new Error("Unsupported random color mode")}static test(t){return"string"==typeof t&&(Q.test(t)||J.test(t))}cmyk(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=Math.min(1-i,1-r,1-s);if(1===o)return new ht(0,0,0,1,"cmyk");return new ht((1-i-o)/(1-o),(1-r-o)/(1-o),(1-s-o)/(1-o),o,"cmyk")}hsl(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=Math.max(i,r,s),h=Math.min(i,r,s),u=(o+h)/2,a=o===h,l=o-h;return new ht(360*(a?0:o===i?((r-s)/l+(r<s?6:0))/6:o===r?((s-i)/l+2)/6:o===s?((i-r)/l+4)/6:0),100*(a?0:u>.5?l/(2-o-h):l/(o+h)),100*u,"hsl")}init(t=0,e=0,n=0,i=0,r="rgb"){if(t=t||0,this.space)for(const t in this.space)delete this[this.space[t]];if("number"==typeof t)r="string"==typeof i?i:r,i="string"==typeof i?0:i,Object.assign(this,{_a:t,_b:e,_c:n,_d:i,space:r});else if(t instanceof Array)this.space=e||("string"==typeof t[3]?t[3]:t[4])||"rgb",Object.assign(this,{_a:t[0],_b:t[1],_c:t[2],_d:t[3]||0});else if(t instanceof Object){const n=function(t,e){const n=st(t,"rgb")?{_a:t.r,_b:t.g,_c:t.b,_d:0,space:"rgb"}:st(t,"xyz")?{_a:t.x,_b:t.y,_c:t.z,_d:0,space:"xyz"}:st(t,"hsl")?{_a:t.h,_b:t.s,_c:t.l,_d:0,space:"hsl"}:st(t,"lab")?{_a:t.l,_b:t.a,_c:t.b,_d:0,space:"lab"}:st(t,"lch")?{_a:t.l,_b:t.c,_c:t.h,_d:0,space:"lch"}:st(t,"cmyk")?{_a:t.c,_b:t.m,_c:t.y,_d:t.k,space:"cmyk"}:{_a:0,_b:0,_c:0,space:"rgb"};return n.space=e||n.space,n}(t,e);Object.assign(this,n)}else if("string"==typeof t)if(J.test(t)){const e=t.replace(W,""),[n,i,r]=V.exec(e).slice(1,4).map((t=>parseInt(t)));Object.assign(this,{_a:n,_b:i,_c:r,_d:0,space:"rgb"})}else{if(!Q.test(t))throw Error("Unsupported string format, can't construct Color");{const e=t=>parseInt(t,16),[,n,i,r]=H.exec(function(t){return 4===t.length?["#",t.substring(1,2),t.substring(1,2),t.substring(2,3),t.substring(2,3),t.substring(3,4),t.substring(3,4)].join(""):t}(t)).map(e);Object.assign(this,{_a:n,_b:i,_c:r,_d:0,space:"rgb"})}}const{_a:s,_b:o,_c:h,_d:u}=this,a="rgb"===this.space?{r:s,g:o,b:h}:"xyz"===this.space?{x:s,y:o,z:h}:"hsl"===this.space?{h:s,s:o,l:h}:"lab"===this.space?{l:s,a:o,b:h}:"lch"===this.space?{l:s,c:o,h:h}:"cmyk"===this.space?{c:s,m:o,y:h,k:u}:{};Object.assign(this,a)}lab(){const{x:t,y:e,z:n}=this.xyz();return new ht(116*e-16,500*(t-e),200*(e-n),"lab")}lch(){const{l:t,a:e,b:n}=this.lab(),i=Math.sqrt(e**2+n**2);let r=180*Math.atan2(n,e)/Math.PI;r<0&&(r*=-1,r=360-r);return new ht(t,i,r,"lch")}rgb(){if("rgb"===this.space)return this;if("lab"===(t=this.space)||"xyz"===t||"lch"===t){let{x:t,y:e,z:n}=this;if("lab"===this.space||"lch"===this.space){let{l:i,a:r,b:s}=this;if("lch"===this.space){const{c:t,h:e}=this,n=Math.PI/180;r=t*Math.cos(n*e),s=t*Math.sin(n*e)}const o=(i+16)/116,h=r/500+o,u=o-s/200,a=16/116,l=.008856,c=7.787;t=.95047*(h**3>l?h**3:(h-a)/c),e=1*(o**3>l?o**3:(o-a)/c),n=1.08883*(u**3>l?u**3:(u-a)/c)}const i=3.2406*t+-1.5372*e+-.4986*n,r=-.9689*t+1.8758*e+.0415*n,s=.0557*t+-.204*e+1.057*n,o=Math.pow,h=.0031308,u=i>h?1.055*o(i,1/2.4)-.055:12.92*i,a=r>h?1.055*o(r,1/2.4)-.055:12.92*r,l=s>h?1.055*o(s,1/2.4)-.055:12.92*s;return new ht(255*u,255*a,255*l)}if("hsl"===this.space){let{h:t,s:e,l:n}=this;if(t/=360,e/=100,n/=100,0===e){n*=255;return new ht(n,n,n)}const i=n<.5?n*(1+e):n+e-n*e,r=2*n-i,s=255*ot(r,i,t+1/3),o=255*ot(r,i,t),h=255*ot(r,i,t-1/3);return new ht(s,o,h)}if("cmyk"===this.space){const{c:t,m:e,y:n,k:i}=this,r=255*(1-Math.min(1,t*(1-i)+i)),s=255*(1-Math.min(1,e*(1-i)+i)),o=255*(1-Math.min(1,n*(1-i)+i));return new ht(r,s,o)}return this;var t}toArray(){const{_a:t,_b:e,_c:n,_d:i,space:r}=this;return[t,e,n,i,r]}toHex(){const[t,e,n]=this._clamped().map(rt);return`#${t}${e}${n}`}toRgb(){const[t,e,n]=this._clamped();return`rgb(${t},${e},${n})`}toString(){return this.toHex()}xyz(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92,h=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,u=s>.04045?Math.pow((s+.055)/1.055,2.4):s/12.92,a=(.4124*o+.3576*h+.1805*u)/.95047,l=(.2126*o+.7152*h+.0722*u)/1,c=(.0193*o+.1192*h+.9505*u)/1.08883,f=a>.008856?Math.pow(a,1/3):7.787*a+16/116,d=l>.008856?Math.pow(l,1/3):7.787*l+16/116,m=c>.008856?Math.pow(c,1/3):7.787*c+16/116;return new ht(f,d,m,"xyz")}_clamped(){const{_a:t,_b:e,_c:n}=this.rgb(),{max:i,min:r,round:s}=Math;return[t,e,n].map((t=>i(0,r(s(t),255))))}}class ut{constructor(...t){this.init(...t)}clone(){return new ut(this)}init(t,e){const n=0,i=0,r=Array.isArray(t)?{x:t[0],y:t[1]}:"object"==typeof t?{x:t.x,y:t.y}:{x:t,y:e};return this.x=null==r.x?n:r.x,this.y=null==r.y?i:r.y,this}toArray(){return[this.x,this.y]}transform(t){return this.clone().transformO(t)}transformO(t){lt.isMatrixLike(t)||(t=new lt(t));const{x:e,y:n}=this;return this.x=t.a*e+t.c*n+t.e,this.y=t.b*e+t.d*n+t.f,this}}function at(t,e,n){return Math.abs(e-t)<1e-6}class lt{constructor(...t){this.init(...t)}static formatTransforms(t){const e="both"===t.flip||!0===t.flip,n=t.flip&&(e||"x"===t.flip)?-1:1,i=t.flip&&(e||"y"===t.flip)?-1:1,r=t.skew&&t.skew.length?t.skew[0]:isFinite(t.skew)?t.skew:isFinite(t.skewX)?t.skewX:0,s=t.skew&&t.skew.length?t.skew[1]:isFinite(t.skew)?t.skew:isFinite(t.skewY)?t.skewY:0,o=t.scale&&t.scale.length?t.scale[0]*n:isFinite(t.scale)?t.scale*n:isFinite(t.scaleX)?t.scaleX*n:n,h=t.scale&&t.scale.length?t.scale[1]*i:isFinite(t.scale)?t.scale*i:isFinite(t.scaleY)?t.scaleY*i:i,u=t.shear||0,a=t.rotate||t.theta||0,l=new ut(t.origin||t.around||t.ox||t.originX,t.oy||t.originY),c=l.x,f=l.y,d=new ut(t.position||t.px||t.positionX||NaN,t.py||t.positionY||NaN),m=d.x,p=d.y,y=new ut(t.translate||t.tx||t.translateX,t.ty||t.translateY),w=y.x,g=y.y,_=new ut(t.relative||t.rx||t.relativeX,t.ry||t.relativeY);return{scaleX:o,scaleY:h,skewX:r,skewY:s,shear:u,theta:a,rx:_.x,ry:_.y,tx:w,ty:g,ox:c,oy:f,px:m,py:p}}static fromArray(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}static isMatrixLike(t){return null!=t.a||null!=t.b||null!=t.c||null!=t.d||null!=t.e||null!=t.f}static matrixMultiply(t,e,n){const i=t.a*e.a+t.c*e.b,r=t.b*e.a+t.d*e.b,s=t.a*e.c+t.c*e.d,o=t.b*e.c+t.d*e.d,h=t.e+t.a*e.e+t.c*e.f,u=t.f+t.b*e.e+t.d*e.f;return n.a=i,n.b=r,n.c=s,n.d=o,n.e=h,n.f=u,n}around(t,e,n){return this.clone().aroundO(t,e,n)}aroundO(t,e,n){const i=t||0,r=e||0;return this.translateO(-i,-r).lmultiplyO(n).translateO(i,r)}clone(){return new lt(this)}decompose(t=0,e=0){const n=this.a,i=this.b,r=this.c,s=this.d,o=this.e,h=this.f,u=n*s-i*r,a=u>0?1:-1,l=a*Math.sqrt(n*n+i*i),c=Math.atan2(a*i,a*n),f=180/Math.PI*c,d=Math.cos(c),m=Math.sin(c),p=(n*r+i*s)/u,y=r*l/(p*n-i)||s*l/(p*i+n);return{scaleX:l,scaleY:y,shear:p,rotate:f,translateX:o-t+t*d*l+e*(p*d*l-m*y),translateY:h-e+t*m*l+e*(p*m*l+d*y),originX:t,originY:e,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}equals(t){if(t===this)return!0;const e=new lt(t);return at(this.a,e.a)&&at(this.b,e.b)&&at(this.c,e.c)&&at(this.d,e.d)&&at(this.e,e.e)&&at(this.f,e.f)}flip(t,e){return this.clone().flipO(t,e)}flipO(t,e){return"x"===t?this.scaleO(-1,1,e,0):"y"===t?this.scaleO(1,-1,0,e):this.scaleO(-1,-1,t,e||t)}init(t){const e=lt.fromArray([1,0,0,1,0,0]);return t=t instanceof Element?t.matrixify():"string"==typeof t?lt.fromArray(t.split(et).map(parseFloat)):Array.isArray(t)?lt.fromArray(t):"object"==typeof t&&lt.isMatrixLike(t)?t:"object"==typeof t?(new lt).transform(t):6===arguments.length?lt.fromArray([].slice.call(arguments)):e,this.a=null!=t.a?t.a:e.a,this.b=null!=t.b?t.b:e.b,this.c=null!=t.c?t.c:e.c,this.d=null!=t.d?t.d:e.d,this.e=null!=t.e?t.e:e.e,this.f=null!=t.f?t.f:e.f,this}inverse(){return this.clone().inverseO()}inverseO(){const t=this.a,e=this.b,n=this.c,i=this.d,r=this.e,s=this.f,o=t*i-e*n;if(!o)throw new Error("Cannot invert "+this);const h=i/o,u=-e/o,a=-n/o,l=t/o,c=-(h*r+a*s),f=-(u*r+l*s);return this.a=h,this.b=u,this.c=a,this.d=l,this.e=c,this.f=f,this}lmultiply(t){return this.clone().lmultiplyO(t)}lmultiplyO(t){const e=t instanceof lt?t:new lt(t);return lt.matrixMultiply(e,this,this)}multiply(t){return this.clone().multiplyO(t)}multiplyO(t){const e=t instanceof lt?t:new lt(t);return lt.matrixMultiply(this,e,this)}rotate(t,e,n){return this.clone().rotateO(t,e,n)}rotateO(t,e=0,n=0){t=h(t);const i=Math.cos(t),r=Math.sin(t),{a:s,b:o,c:u,d:a,e:l,f:c}=this;return this.a=s*i-o*r,this.b=o*i+s*r,this.c=u*i-a*r,this.d=a*i+u*r,this.e=l*i-c*r+n*r-e*i+e,this.f=c*i+l*r-e*r-n*i+n,this}scale(){return this.clone().scaleO(...arguments)}scaleO(t,e=t,n=0,i=0){3===arguments.length&&(i=n,n=e,e=t);const{a:r,b:s,c:o,d:h,e:u,f:a}=this;return this.a=r*t,this.b=s*e,this.c=o*t,this.d=h*e,this.e=u*t-n*t+n,this.f=a*e-i*e+i,this}shear(t,e,n){return this.clone().shearO(t,e,n)}shearO(t,e=0,n=0){const{a:i,b:r,c:s,d:o,e:h,f:u}=this;return this.a=i+r*t,this.c=s+o*t,this.e=h+u*t-n*t,this}skew(){return this.clone().skewO(...arguments)}skewO(t,e=t,n=0,i=0){3===arguments.length&&(i=n,n=e,e=t),t=h(t),e=h(e);const r=Math.tan(t),s=Math.tan(e),{a:o,b:u,c:a,d:l,e:c,f:f}=this;return this.a=o+u*r,this.b=u+o*s,this.c=a+l*r,this.d=l+a*s,this.e=c+f*r-i*r,this.f=f+c*s-n*s,this}skewX(t,e,n){return this.skew(t,0,e,n)}skewY(t,e,n){return this.skew(0,t,e,n)}toArray(){return[this.a,this.b,this.c,this.d,this.e,this.f]}toString(){return"matrix("+this.a+","+this.b+","+this.c+","+this.d+","+this.e+","+this.f+")"}transform(t){if(lt.isMatrixLike(t)){return new lt(t).multiplyO(this)}const e=lt.formatTransforms(t),{x:n,y:i}=new ut(e.ox,e.oy).transform(this),r=(new lt).translateO(e.rx,e.ry).lmultiplyO(this).translateO(-n,-i).scaleO(e.scaleX,e.scaleY).skewO(e.skewX,e.skewY).shearO(e.shear).rotateO(e.theta).translateO(n,i);if(isFinite(e.px)||isFinite(e.py)){const t=new ut(n,i).transform(r),s=isFinite(e.px)?e.px-t.x:0,o=isFinite(e.py)?e.py-t.y:0;r.translateO(s,o)}return r.translateO(e.tx,e.ty),r}translate(t,e){return this.clone().translateO(t,e)}translateO(t,e){return this.e+=t||0,this.f+=e||0,this}valueOf(){return{a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}}function ct(){if(!ct.nodes){const t=j().size(2,0);t.node.style.cssText=["opacity: 0","position: absolute","left: -100%","top: -100%","overflow: hidden"].join(";"),t.attr("focusable","false"),t.attr("aria-hidden","true");const e=t.path().node;ct.nodes={svg:t,path:e}}if(!ct.nodes.svg.node.parentNode){const t=b.document.body||b.document.documentElement;ct.nodes.svg.addTo(t)}return ct.nodes}function ft(t){return!(t.width||t.height||t.x||t.y)}P(lt,"Matrix");class dt{constructor(...t){this.init(...t)}addOffset(){return this.x+=b.window.pageXOffset,this.y+=b.window.pageYOffset,new dt(this)}init(t){return t="string"==typeof t?t.split(et).map(parseFloat):Array.isArray(t)?t:"object"==typeof t?[null!=t.left?t.left:t.x,null!=t.top?t.top:t.y,t.width,t.height]:4===arguments.length?[].slice.call(arguments):[0,0,0,0],this.x=t[0]||0,this.y=t[1]||0,this.width=this.w=t[2]||0,this.height=this.h=t[3]||0,this.x2=this.x+this.w,this.y2=this.y+this.h,this.cx=this.x+this.w/2,this.cy=this.y+this.h/2,this}isNulled(){return ft(this)}merge(t){const e=Math.min(this.x,t.x),n=Math.min(this.y,t.y),i=Math.max(this.x+this.width,t.x+t.width)-e,r=Math.max(this.y+this.height,t.y+t.height)-n;return new dt(e,n,i,r)}toArray(){return[this.x,this.y,this.width,this.height]}toString(){return this.x+" "+this.y+" "+this.width+" "+this.height}transform(t){t instanceof lt||(t=new lt(t));let e=1/0,n=-1/0,i=1/0,r=-1/0;return[new ut(this.x,this.y),new ut(this.x2,this.y),new ut(this.x,this.y2),new ut(this.x2,this.y2)].forEach((function(s){s=s.transform(t),e=Math.min(e,s.x),n=Math.max(n,s.x),i=Math.min(i,s.y),r=Math.max(r,s.y)})),new dt(e,i,n-e,r-i)}}function mt(t,e,n){let i;try{if(i=e(t.node),ft(i)&&((r=t.node)!==b.document&&!(b.document.documentElement.contains||function(t){for(;t.parentNode;)t=t.parentNode;return t===b.document}).call(b.document.documentElement,r)))throw new Error("Element not in the dom")}catch(e){i=n(t)}var r;return i}n({viewbox:{viewbox(t,e,n,i){return null==t?new dt(this.attr("viewBox")):this.attr("viewBox",new dt(t,e,n,i))},zoom(t,e){let{width:n,height:i}=this.attr(["width","height"]);if((n||i)&&"string"!=typeof n&&"string"!=typeof i||(n=this.node.clientWidth,i=this.node.clientHeight),!n||!i)throw new Error("Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element");const r=this.viewbox(),s=n/r.width,o=i/r.height,h=Math.min(s,o);if(null==t)return h;let u=h/t;u===1/0&&(u=Number.MAX_SAFE_INTEGER/100),e=e||new ut(n/2/s+r.x,i/2/o+r.y);const a=new dt(r).transform(new lt({scale:u,origin:e}));return this.viewbox(a)}}}),P(dt,"Box");class pt extends Array{constructor(t=[],...e){if(super(t,...e),"number"==typeof t)return this;this.length=0,this.push(...t)}}X([pt],{each(t,...e){return"function"==typeof t?this.map(((e,n,i)=>t.call(e,e,n,i))):this.map((n=>n[t](...e)))},toArray(){return Array.prototype.concat.apply([],this)}});const yt=["toArray","constructor","each"];function wt(t,e){return new pt(s((e||b.document).querySelectorAll(t),(function(t){return I(t)})))}pt.extend=function(t){t=t.reduce(((t,e)=>(yt.includes(e)||"_"===e[0]||(e in Array.prototype&&(t["$"+e]=Array.prototype[e]),t[e]=function(...t){return this.each(e,...t)}),t)),{}),X([pt],t)};let gt=0;const _t={};function xt(t){let e=t.getEventHolder();return e===b.window&&(e=_t),e.events||(e.events={}),e.events}function bt(t){return t.getEventTarget()}function vt(t){let e=t.getEventHolder();e===b.window&&(e=_t),e.events&&(e.events={})}function Mt(t,e,n,i,r){const s=n.bind(i||t),o=j(t),h=xt(o),u=bt(o);e=Array.isArray(e)?e:e.split(et),n._svgjsListenerId||(n._svgjsListenerId=++gt),e.forEach((function(t){const e=t.split(".")[0],i=t.split(".")[1]||"*";h[e]=h[e]||{},h[e][i]=h[e][i]||{},h[e][i][n._svgjsListenerId]=s,u.addEventListener(e,s,r||!1)}))}function At(t,e,n,i){const r=j(t),s=xt(r),o=bt(r);("function"!=typeof n||(n=n._svgjsListenerId))&&(e=Array.isArray(e)?e:(e||"").split(et)).forEach((function(t){const e=t&&t.split(".")[0],h=t&&t.split(".")[1];let u,a;if(n)s[e]&&s[e][h||"*"]&&(o.removeEventListener(e,s[e][h||"*"][n],i||!1),delete s[e][h||"*"][n]);else if(e&&h){if(s[e]&&s[e][h]){for(a in s[e][h])At(o,[e,h].join("."),a);delete s[e][h]}}else if(h)for(t in s)for(u in s[t])h===u&&At(o,[t,h].join("."));else if(e){if(s[e]){for(u in s[e])At(o,[e,u].join("."));delete s[e]}}else{for(t in s)At(o,t);vt(r)}}))}function Ot(t,e,n,i){const r=bt(t);return e instanceof b.window.Event||(e=new b.window.CustomEvent(e,{detail:n,cancelable:!0,...i})),r.dispatchEvent(e),e}class kt extends C{addEventListener(){}dispatch(t,e,n){return Ot(this,t,e,n)}dispatchEvent(t){const e=this.getEventHolder().events;if(!e)return!0;const n=e[t.type];for(const e in n)for(const i in n[e])n[e][i](t);return!t.defaultPrevented}fire(t,e,n){return this.dispatch(t,e,n),this}getEventHolder(){return this}getEventTarget(){return this}off(t,e,n){return At(this,t,e,n),this}on(t,e,n,i){return Mt(this,t,e,n,i),this}removeEventListener(){}}function Tt(){}P(kt,"EventTarget");const Ct={duration:400,ease:">",delay:0},Nt={"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","text-anchor":"start"};var St={__proto__:null,attrs:Nt,noop:Tt,timeline:Ct};class Et extends Array{constructor(...t){super(...t),this.init(...t)}clone(){return new this.constructor(this)}init(t){return"number"==typeof t||(this.length=0,this.push(...this.parse(t))),this}parse(t=[]){return t instanceof Array?t:t.trim().split(et).map(parseFloat)}toArray(){return Array.prototype.concat.apply([],this)}toSet(){return new Set(this)}toString(){return this.join(" ")}valueOf(){const t=[];return t.push(...this),t}}class jt{constructor(...t){this.init(...t)}convert(t){return new jt(this.value,t)}divide(t){return t=new jt(t),new jt(this/t,this.unit||t.unit)}init(t,e){return e=Array.isArray(t)?t[1]:e,t=Array.isArray(t)?t[0]:t,this.value=0,this.unit=e||"","number"==typeof t?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:"string"==typeof t?(e=t.match(B))&&(this.value=parseFloat(e[1]),"%"===e[5]?this.value/=100:"s"===e[5]&&(this.value*=1e3),this.unit=e[5]):t instanceof jt&&(this.value=t.valueOf(),this.unit=t.unit),this}minus(t){return t=new jt(t),new jt(this-t,this.unit||t.unit)}plus(t){return t=new jt(t),new jt(this+t,this.unit||t.unit)}times(t){return t=new jt(t),new jt(this*t,this.unit||t.unit)}toArray(){return[this.value,this.unit]}toJSON(){return this.toString()}toString(){return("%"===this.unit?~~(1e8*this.value)/1e6:"s"===this.unit?this.value/1e3:this.value)+this.unit}valueOf(){return this.value}}const Dt=new Set(["fill","stroke","color","bgcolor","stop-color","flood-color","lighting-color"]),It=[];class Dom extends kt{constructor(t,e){super(),this.node=t,this.type=t.nodeName,e&&t!==e&&this.attr(e)}add(t,e){return(t=j(t)).removeNamespace&&this.node instanceof b.window.SVGElement&&t.removeNamespace(),null==e?this.node.appendChild(t.node):t.node!==this.node.childNodes[e]&&this.node.insertBefore(t.node,this.node.childNodes[e]),this}addTo(t,e){return j(t).put(this,e)}children(){return new pt(s(this.node.children,(function(t){return I(t)})))}clear(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this}clone(t=!0,e=!0){this.writeDataToDom();let n=this.node.cloneNode(t);return e&&(n=F(n)),new this.constructor(n)}each(t,e){const n=this.children();let i,r;for(i=0,r=n.length;i<r;i++)t.apply(n[i],[i,n]),e&&n[i].each(t,e);return this}element(t,e){return this.put(new Dom(E(t),e))}first(){return I(this.node.firstChild)}get(t){return I(this.node.childNodes[t])}getEventHolder(){return this.node}getEventTarget(){return this.node}has(t){return this.index(t)>=0}html(t,e){return this.xml(t,e,w)}id(t){return void 0!==t||this.node.id||(this.node.id=q(this.type)),this.attr("id",t)}index(t){return[].slice.call(this.node.childNodes).indexOf(t.node)}last(){return I(this.node.lastChild)}matches(t){const e=this.node,n=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector||null;return n&&n.call(e,t)}parent(t){let e=this;if(!e.node.parentNode)return null;if(e=I(e.node.parentNode),!t)return e;do{if("string"==typeof t?e.matches(t):e instanceof t)return e}while(e=I(e.node.parentNode));return e}put(t,e){return t=j(t),this.add(t,e),t}putIn(t,e){return j(t).add(this,e)}remove(){return this.parent()&&this.parent().removeElement(this),this}removeElement(t){return this.node.removeChild(t.node),this}replace(t){return t=j(t),this.node.parentNode&&this.node.parentNode.replaceChild(t.node,this.node),t}round(t=2,e=null){const n=10**t,i=this.attr(e);for(const t in i)"number"==typeof i[t]&&(i[t]=Math.round(i[t]*n)/n);return this.attr(i),this}svg(t,e){return this.xml(t,e,y)}toString(){return this.id()}words(t){return this.node.textContent=t,this}wrap(t){const e=this.parent();if(!e)return this.addTo(t);const n=e.index(this);return e.put(t,n).put(this)}writeDataToDom(){return this.each((function(){this.writeDataToDom()})),this}xml(t,e,n){if("boolean"==typeof t&&(n=e,e=t,t=null),null==t||"function"==typeof t){e=null==e||e,this.writeDataToDom();let n=this;if(null!=t){if(n=I(n.node.cloneNode(!0)),e){const e=t(n);if(n=e||n,!1===e)return""}n.each((function(){const e=t(this),n=e||this;!1===e?this.remove():e&&this!==n&&this.replace(n)}),!0)}return e?n.node.outerHTML:n.node.innerHTML}e=null!=e&&e;const i=E("wrapper",n),r=b.document.createDocumentFragment();i.innerHTML=t;for(let t=i.children.length;t--;)r.appendChild(i.firstElementChild);const s=this.parent();return e?this.replace(r)&&s:this.add(r)}}X(Dom,{attr:function(t,e,n){if(null==t){t={},e=this.node.attributes;for(const n of e)t[n.nodeName]=K.test(n.nodeValue)?parseFloat(n.nodeValue):n.nodeValue;return t}if(t instanceof Array)return t.reduce(((t,e)=>(t[e]=this.attr(e),t)),{});if("object"==typeof t&&t.constructor===Object)for(e in t)this.attr(e,t[e]);else if(null===e)this.node.removeAttribute(t);else{if(null==e)return null==(e=this.node.getAttribute(t))?Nt[t]:K.test(e)?parseFloat(e):e;"number"==typeof(e=It.reduce(((e,n)=>n(t,e,this)),e))?e=new jt(e):Dt.has(t)&&ht.isColor(e)?e=new ht(e):e.constructor===Array&&(e=new Et(e)),"leading"===t?this.leading&&this.leading(e):"string"==typeof n?this.node.setAttributeNS(n,t,e.toString()):this.node.setAttribute(t,e.toString()),!this.rebuild||"font-size"!==t&&"x"!==t||this.rebuild()}return this},find:function(t){return wt(t,this.node)},findOne:function(t){return I(this.node.querySelector(t))}}),P(Dom,"Dom");class Element extends Dom{constructor(t,e){super(t,e),this.dom={},this.node.instance=this,(t.hasAttribute("data-svgjs")||t.hasAttribute("svgjs:data"))&&this.setData(JSON.parse(t.getAttribute("data-svgjs"))??JSON.parse(t.getAttribute("svgjs:data"))??{})}center(t,e){return this.cx(t).cy(e)}cx(t){return null==t?this.x()+this.width()/2:this.x(t-this.width()/2)}cy(t){return null==t?this.y()+this.height()/2:this.y(t-this.height()/2)}defs(){const t=this.root();return t&&t.defs()}dmove(t,e){return this.dx(t).dy(e)}dx(t=0){return this.x(new jt(t).plus(this.x()))}dy(t=0){return this.y(new jt(t).plus(this.y()))}getEventHolder(){return this}height(t){return this.attr("height",t)}move(t,e){return this.x(t).y(e)}parents(t=this.root()){const e="string"==typeof t;e||(t=j(t));const n=new pt;let i=this;for(;(i=i.parent())&&i.node!==b.document&&"#document-fragment"!==i.nodeName&&(n.push(i),e||i.node!==t.node)&&(!e||!i.matches(t));)if(i.node===this.root().node)return null;return n}reference(t){if(!(t=this.attr(t)))return null;const e=(t+"").match($);return e?j(e[1]):null}root(){const t=this.parent(R(S));return t&&t.root()}setData(t){return this.dom=t,this}size(t,e){const n=l(this,t,e);return this.width(new jt(n.width)).height(new jt(n.height))}width(t){return this.attr("width",t)}writeDataToDom(){return m(this,this.dom),super.writeDataToDom()}x(t){return this.attr("x",t)}y(t){return this.attr("y",t)}}X(Element,{bbox:function(){const t=mt(this,(t=>t.getBBox()),(t=>{try{const e=t.clone().addTo(ct().svg).show(),n=e.node.getBBox();return e.remove(),n}catch(e){throw new Error(`Getting bbox of element "${t.node.nodeName}" is not possible: ${e.toString()}`)}}));return new dt(t)},rbox:function(t){const e=mt(this,(t=>t.getBoundingClientRect()),(t=>{throw new Error(`Getting rbox of element "${t.node.nodeName}" is not possible`)})),n=new dt(e);return t?n.transform(t.screenCTM().inverseO()):n.addOffset()},inside:function(t,e){const n=this.bbox();return t>n.x&&e>n.y&&t<n.x+n.width&&e<n.y+n.height},point:function(t,e){return new ut(t,e).transformO(this.screenCTM().inverseO())},ctm:function(){return new lt(this.node.getCTM())},screenCTM:function(){try{if("function"==typeof this.isRoot&&!this.isRoot()){const t=this.rect(1,1),e=t.node.getScreenCTM();return t.remove(),new lt(e)}return new lt(this.node.getScreenCTM())}catch(t){return console.warn(`Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`),new lt}}}),P(Element,"Element");const zt={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(t,e){return"color"===e?t:t+"-"+e}};["fill","stroke"].forEach((function(t){const e={};let i;e[t]=function(e){if(void 0===e)return this.attr(t);if("string"==typeof e||e instanceof ht||ht.isRgb(e)||e instanceof Element)this.attr(t,e);else for(i=zt[t].length-1;i>=0;i--)null!=e[zt[t][i]]&&this.attr(zt.prefix(t,zt[t][i]),e[zt[t][i]]);return this},n(["Element","Runner"],e)})),n(["Element","Runner"],{matrix:function(t,e,n,i,r,s){return null==t?new lt(this):this.attr("transform",new lt(t,e,n,i,r,s))},rotate:function(t,e,n){return this.transform({rotate:t,ox:e,oy:n},!0)},skew:function(t,e,n,i){return 1===arguments.length||3===arguments.length?this.transform({skew:t,ox:e,oy:n},!0):this.transform({skew:[t,e],ox:n,oy:i},!0)},shear:function(t,e,n){return this.transform({shear:t,ox:e,oy:n},!0)},scale:function(t,e,n,i){return 1===arguments.length||3===arguments.length?this.transform({scale:t,ox:e,oy:n},!0):this.transform({scale:[t,e],ox:n,oy:i},!0)},translate:function(t,e){return this.transform({translate:[t,e]},!0)},relative:function(t,e){return this.transform({relative:[t,e]},!0)},flip:function(t="both",e="center"){return-1==="xybothtrue".indexOf(t)&&(e=t,t="both"),this.transform({flip:t,origin:e},!0)},opacity:function(t){return this.attr("opacity",t)}}),n("radius",{radius:function(t,e=t){return"radialGradient"===(this._element||this).type?this.attr("r",new jt(t)):this.rx(t).ry(e)}}),n("Path",{length:function(){return this.node.getTotalLength()},pointAt:function(t){return new ut(this.node.getPointAtLength(t))}}),n(["Element","Runner"],{font:function(t,e){if("object"==typeof t){for(e in t)this.font(e,t[e]);return this}return"leading"===t?this.leading(e):"anchor"===t?this.attr("text-anchor",e):"size"===t||"family"===t||"weight"===t||"stretch"===t||"variant"===t||"style"===t?this.attr("font-"+t,e):this.attr(t,e)}});n("Element",["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","mouseenter","mouseleave","touchstart","touchmove","touchleave","touchend","touchcancel","contextmenu","wheel","pointerdown","pointermove","pointerup","pointerleave","pointercancel"].reduce((function(t,e){return t[e]=function(t){return null===t?this.off(e):this.on(e,t),this},t}),{})),n("Element",{untransform:function(){return this.attr("transform",null)},matrixify:function(){const t=(this.attr("transform")||"").split(U).slice(0,-1).map((function(t){const e=t.trim().split("(");return[e[0],e[1].split(et).map((function(t){return parseFloat(t)}))]})).reverse().reduce((function(t,e){return"matrix"===e[0]?t.lmultiply(lt.fromArray(e[1])):t[e[0]].apply(t,e[1])}),new lt);return t},toParent:function(t,e){if(this===t)return this;if(d(this.node))return this.addTo(t,e);const n=this.screenCTM(),i=t.screenCTM().inverse();return this.addTo(t,e).untransform().transform(i.multiply(n)),this},toRoot:function(t){return this.toParent(this.root(),t)},transform:function(t,e){if(null==t||"string"==typeof t){const e=new lt(this).decompose();return null==t?e:e[t]}lt.isMatrixLike(t)||(t={...t,origin:c(t,this)});const n=new lt(!0===e?this:e||!1).transform(t);return this.attr("transform",n)}});class Container extends Element{flatten(){return this.each((function(){if(this instanceof Container)return this.flatten().ungroup()})),this}ungroup(t=this.parent(),e=t.index(this)){return e=-1===e?t.children().length:e,this.each((function(n,i){return i[i.length-n-1].toParent(t,e)})),this.remove()}}P(Container,"Container");class Defs extends Container{constructor(t,e=t){super(D("defs",t),e)}flatten(){return this}ungroup(){return this}}P(Defs,"Defs");class Shape extends Element{}function Pt(t){return this.attr("rx",t)}function Rt(t){return this.attr("ry",t)}function Lt(t){return null==t?this.cx()-this.rx():this.cx(t+this.rx())}function qt(t){return null==t?this.cy()-this.ry():this.cy(t+this.ry())}function Ft(t){return this.attr("cx",t)}function Xt(t){return this.attr("cy",t)}function Yt(t){return null==t?2*this.rx():this.rx(new jt(t).divide(2))}function Bt(t){return null==t?2*this.ry():this.ry(new jt(t).divide(2))}P(Shape,"Shape");var Gt={__proto__:null,cx:Ft,cy:Xt,height:Bt,rx:Pt,ry:Rt,width:Yt,x:Lt,y:qt};class Ellipse extends Shape{constructor(t,e=t){super(D("ellipse",t),e)}size(t,e){const n=l(this,t,e);return this.rx(new jt(n.width).divide(2)).ry(new jt(n.height).divide(2))}}X(Ellipse,Gt),n("Container",{ellipse:Y((function(t=0,e=t){return this.put(new Ellipse).size(t,e).move(0,0)}))}),P(Ellipse,"Ellipse");class Ht extends Dom{constructor(t=b.document.createDocumentFragment()){super(t)}xml(t,e,n){if("boolean"==typeof t&&(n=e,e=t,t=null),null==t||"function"==typeof t){const t=new Dom(E("wrapper",n));return t.add(this.node.cloneNode(!0)),t.xml(!1,n)}return super.xml(t,!1,n)}}function Vt(t,e){return"radialGradient"===(this._element||this).type?this.attr({fx:new jt(t),fy:new jt(e)}):this.attr({x1:new jt(t),y1:new jt(e)})}function $t(t,e){return"radialGradient"===(this._element||this).type?this.attr({cx:new jt(t),cy:new jt(e)}):this.attr({x2:new jt(t),y2:new jt(e)})}P(Ht,"Fragment");var Ut,Wt={__proto__:null,from:Vt,to:$t};class Gradient extends Container{constructor(t,e){super(D(t+"Gradient","string"==typeof t?null:t),e)}attr(t,e,n){return"transform"===t&&(t="gradientTransform"),super.attr(t,e,n)}bbox(){return new dt}targets(){return wt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}X(Gradient,Wt),n({Container:{gradient(...t){return this.defs().gradient(...t)}},Defs:{gradient:Y((function(t,e){return this.put(new Gradient(t)).update(e)}))}}),P(Gradient,"Gradient");class Pattern extends Container{constructor(t,e=t){super(D("pattern",t),e)}attr(t,e,n){return"transform"===t&&(t="patternTransform"),super.attr(t,e,n)}bbox(){return new dt}targets(){return wt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}n({Container:{pattern(...t){return this.defs().pattern(...t)}},Defs:{pattern:Y((function(t,e,n){return this.put(new Pattern).update(n).attr({x:0,y:0,width:t,height:e,patternUnits:"userSpaceOnUse"})}))}}),P(Pattern,"Pattern");class Image extends Shape{constructor(t,e=t){super(D("image",t),e)}load(t,e){if(!t)return this;const n=new b.window.Image;return Mt(n,"load",(function(t){const i=this.parent(Pattern);0===this.width()&&0===this.height()&&this.size(n.width,n.height),i instanceof Pattern&&0===i.width()&&0===i.height()&&i.size(this.width(),this.height()),"function"==typeof e&&e.call(this,t)}),this),Mt(n,"load error",(function(){At(n)})),this.attr("href",n.src=t,_)}}Ut=function(t,e,n){return"fill"!==t&&"stroke"!==t||tt.test(e)&&(e=n.root().defs().image(e)),e instanceof Image&&(e=n.root().defs().pattern(0,0,(t=>{t.add(e)}))),e},It.push(Ut),n({Container:{image:Y((function(t,e){return this.put(new Image).size(0,0).load(t,e)}))}}),P(Image,"Image");class Qt extends Et{bbox(){let t=-1/0,e=-1/0,n=1/0,i=1/0;return this.forEach((function(r){t=Math.max(r[0],t),e=Math.max(r[1],e),n=Math.min(r[0],n),i=Math.min(r[1],i)})),new dt(n,i,t-n,e-i)}move(t,e){const n=this.bbox();if(t-=n.x,e-=n.y,!isNaN(t)&&!isNaN(e))for(let n=this.length-1;n>=0;n--)this[n]=[this[n][0]+t,this[n][1]+e];return this}parse(t=[0,0]){const e=[];(t=t instanceof Array?Array.prototype.concat.apply([],t):t.trim().split(et).map(parseFloat)).length%2!=0&&t.pop();for(let n=0,i=t.length;n<i;n+=2)e.push([t[n],t[n+1]]);return e}size(t,e){let n;const i=this.bbox();for(n=this.length-1;n>=0;n--)i.width&&(this[n][0]=(this[n][0]-i.x)*t/i.width+i.x),i.height&&(this[n][1]=(this[n][1]-i.y)*e/i.height+i.y);return this}toLine(){return{x1:this[0][0],y1:this[0][1],x2:this[1][0],y2:this[1][1]}}toString(){const t=[];for(let e=0,n=this.length;e<n;e++)t.push(this[e].join(","));return t.join(" ")}transform(t){return this.clone().transformO(t)}transformO(t){lt.isMatrixLike(t)||(t=new lt(t));for(let e=this.length;e--;){const[n,i]=this[e];this[e][0]=t.a*n+t.c*i+t.e,this[e][1]=t.b*n+t.d*i+t.f}return this}}var Jt={__proto__:null,MorphArray:Qt,height:function(t){const e=this.bbox();return null==t?e.height:this.size(e.width,t)},width:function(t){const e=this.bbox();return null==t?e.width:this.size(t,e.height)},x:function(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)},y:function(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)}};class Line extends Shape{constructor(t,e=t){super(D("line",t),e)}array(){return new Qt([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])}move(t,e){return this.attr(this.array().move(t,e).toLine())}plot(t,e,n,i){return null==t?this.array():(t=void 0!==e?{x1:t,y1:e,x2:n,y2:i}:new Qt(t).toLine(),this.attr(t))}size(t,e){const n=l(this,t,e);return this.attr(this.array().size(n.width,n.height).toLine())}}X(Line,Jt),n({Container:{line:Y((function(...t){return Line.prototype.plot.apply(this.put(new Line),null!=t[0]?t:[0,0,0,0])}))}}),P(Line,"Line");class Marker extends Container{constructor(t,e=t){super(D("marker",t),e)}height(t){return this.attr("markerHeight",t)}orient(t){return this.attr("orient",t)}ref(t,e){return this.attr("refX",t).attr("refY",e)}toString(){return"url(#"+this.id()+")"}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}width(t){return this.attr("markerWidth",t)}}function Zt(t,e){return function(n){return null==n?this[t]:(this[t]=n,e&&e.call(this),this)}}n({Container:{marker(...t){return this.defs().marker(...t)}},Defs:{marker:Y((function(t,e,n){return this.put(new Marker).size(t,e).ref(t/2,e/2).viewbox(0,0,t,e).attr("orient","auto").update(n)}))},marker:{marker(t,e,n,i){let r=["marker"];return"all"!==t&&r.push(t),r=r.join("-"),t=arguments[1]instanceof Marker?arguments[1]:this.defs().marker(e,n,i),this.attr(r,t)}}}),P(Marker,"Marker");const Kt={"-":function(t){return t},"<>":function(t){return-Math.cos(t*Math.PI)/2+.5},">":function(t){return Math.sin(t*Math.PI/2)},"<":function(t){return 1-Math.cos(t*Math.PI/2)},bezier:function(t,e,n,i){return function(r){return r<0?t>0?e/t*r:n>0?i/n*r:0:r>1?n<1?(1-i)/(1-n)*r+(i-n)/(1-n):t<1?(1-e)/(1-t)*r+(e-t)/(1-t):1:3*r*(1-r)**2*e+3*r**2*(1-r)*i+r**3}},steps:function(t,e="end"){e=e.split("-").reverse()[0];let n=t;return"none"===e?--n:"both"===e&&++n,(i,r=!1)=>{let s=Math.floor(i*t);const o=i*s%1==0;return"start"!==e&&"both"!==e||++s,r&&o&&--s,i>=0&&s<0&&(s=0),i<=1&&s>n&&(s=n),s/n}}};class te{done(){return!1}}class ee extends te{constructor(t=Ct.ease){super(),this.ease=Kt[t]||t}step(t,e,n){return"number"!=typeof t?n<1?t:e:t+(e-t)*this.ease(n)}}class ne extends te{constructor(t){super(),this.stepper=t}done(t){return t.done}step(t,e,n,i){return this.stepper(t,e,n,i)}}function ie(){const t=(this._duration||500)/1e3,e=this._overshoot||0,n=Math.PI,i=Math.log(e/100+1e-10),r=-i/Math.sqrt(n*n+i*i),s=3.9/(r*t);this.d=2*r*s,this.k=s*s}class re extends ne{constructor(t=500,e=0){super(),this.duration(t).overshoot(e)}step(t,e,n,i){if("string"==typeof t)return t;if(i.done=n===1/0,n===1/0)return e;if(0===n)return t;n>100&&(n=16),n/=1e3;const r=i.velocity||0,s=-this.d*r-this.k*(t-e),o=t+r*n+s*n*n/2;return i.velocity=r+s*n,i.done=Math.abs(e-o)+Math.abs(r)<.002,i.done?e:o}}X(re,{duration:Zt("_duration",ie),overshoot:Zt("_overshoot",ie)});class se extends ne{constructor(t=.1,e=.01,n=0,i=1e3){super(),this.p(t).i(e).d(n).windup(i)}step(t,e,n,i){if("string"==typeof t)return t;if(i.done=n===1/0,n===1/0)return e;if(0===n)return t;const r=e-t;let s=(i.integral||0)+r*n;const o=(r-(i.error||0))/n,h=this._windup;return!1!==h&&(s=Math.max(-h,Math.min(s,h))),i.error=r,i.integral=s,i.done=Math.abs(r)<.001,i.done?e:t+(this.P*r+this.I*s+this.D*o)}}X(se,{windup:Zt("_windup"),p:Zt("P"),i:Zt("I"),d:Zt("D")});const oe={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0},he={M:function(t,e,n){return e.x=n.x=t[0],e.y=n.y=t[1],["M",e.x,e.y]},L:function(t,e){return e.x=t[0],e.y=t[1],["L",t[0],t[1]]},H:function(t,e){return e.x=t[0],["H",t[0]]},V:function(t,e){return e.y=t[0],["V",t[0]]},C:function(t,e){return e.x=t[4],e.y=t[5],["C",t[0],t[1],t[2],t[3],t[4],t[5]]},S:function(t,e){return e.x=t[2],e.y=t[3],["S",t[0],t[1],t[2],t[3]]},Q:function(t,e){return e.x=t[2],e.y=t[3],["Q",t[0],t[1],t[2],t[3]]},T:function(t,e){return e.x=t[0],e.y=t[1],["T",t[0],t[1]]},Z:function(t,e,n){return e.x=n.x,e.y=n.y,["Z"]},A:function(t,e){return e.x=t[5],e.y=t[6],["A",t[0],t[1],t[2],t[3],t[4],t[5],t[6]]}},ue="mlhvqtcsaz".split("");for(let t=0,e=ue.length;t<e;++t)he[ue[t]]=function(t){return function(e,n,i){if("H"===t)e[0]=e[0]+n.x;else if("V"===t)e[0]=e[0]+n.y;else if("A"===t)e[5]=e[5]+n.x,e[6]=e[6]+n.y;else for(let t=0,i=e.length;t<i;++t)e[t]=e[t]+(t%2?n.y:n.x);return he[t](e,n,i)}}(ue[t].toUpperCase());function ae(t){return t.segment.length&&t.segment.length-1===oe[t.segment[0].toUpperCase()]}function le(t,e){t.inNumber&&ce(t,!1);const n=nt.test(e);if(n)t.segment=[e];else{const e=t.lastCommand,n=e.toLowerCase(),i=e===n;t.segment=["m"===n?i?"l":"L":e]}return t.inSegment=!0,t.lastCommand=t.segment[0],n}function ce(t,e){if(!t.inNumber)throw new Error("Parser Error");t.number&&t.segment.push(parseFloat(t.number)),t.inNumber=e,t.number="",t.pointSeen=!1,t.hasExponent=!1,ae(t)&&fe(t)}function fe(t){t.inSegment=!1,t.absolute&&(t.segment=function(t){const e=t.segment[0];return he[e](t.segment.slice(1),t.p,t.p0)}(t)),t.segments.push(t.segment)}function de(t){if(!t.segment.length)return!1;const e="A"===t.segment[0].toUpperCase(),n=t.segment.length;return e&&(4===n||5===n)}function me(t){return"E"===t.lastToken.toUpperCase()}const pe=new Set([" ",",","\t","\n","\r","\f"]);class ye extends Et{bbox(){return ct().path.setAttribute("d",this.toString()),new dt(ct.nodes.path.getBBox())}move(t,e){const n=this.bbox();if(t-=n.x,e-=n.y,!isNaN(t)&&!isNaN(e))for(let n,i=this.length-1;i>=0;i--)n=this[i][0],"M"===n||"L"===n||"T"===n?(this[i][1]+=t,this[i][2]+=e):"H"===n?this[i][1]+=t:"V"===n?this[i][1]+=e:"C"===n||"S"===n||"Q"===n?(this[i][1]+=t,this[i][2]+=e,this[i][3]+=t,this[i][4]+=e,"C"===n&&(this[i][5]+=t,this[i][6]+=e)):"A"===n&&(this[i][6]+=t,this[i][7]+=e);return this}parse(t="M0 0"){return Array.isArray(t)&&(t=Array.prototype.concat.apply([],t).toString()),function(t,e=!0){let n=0,i="";const r={segment:[],inNumber:!1,number:"",lastToken:"",inSegment:!1,segments:[],pointSeen:!1,hasExponent:!1,absolute:e,p0:new ut,p:new ut};for(;r.lastToken=i,i=t.charAt(n++);)if(r.inSegment||!le(r,i))if("."!==i)if(isNaN(parseInt(i)))if(pe.has(i))r.inNumber&&ce(r,!1);else if("-"!==i&&"+"!==i)if("E"!==i.toUpperCase()){if(nt.test(i)){if(r.inNumber)ce(r,!1);else{if(!ae(r))throw new Error("parser Error");fe(r)}--n}}else r.number+=i,r.hasExponent=!0;else{if(r.inNumber&&!me(r)){ce(r,!1),--n;continue}r.number+=i,r.inNumber=!0}else{if("0"===r.number||de(r)){r.inNumber=!0,r.number=i,ce(r,!0);continue}r.inNumber=!0,r.number+=i}else{if(r.pointSeen||r.hasExponent){ce(r,!1),--n;continue}r.inNumber=!0,r.pointSeen=!0,r.number+=i}return r.inNumber&&ce(r,!1),r.inSegment&&ae(r)&&fe(r),r.segments}(t)}size(t,e){const n=this.bbox();let i,r;for(n.width=0===n.width?1:n.width,n.height=0===n.height?1:n.height,i=this.length-1;i>=0;i--)r=this[i][0],"M"===r||"L"===r||"T"===r?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*e/n.height+n.y):"H"===r?this[i][1]=(this[i][1]-n.x)*t/n.width+n.x:"V"===r?this[i][1]=(this[i][1]-n.y)*e/n.height+n.y:"C"===r||"S"===r||"Q"===r?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*e/n.height+n.y,this[i][3]=(this[i][3]-n.x)*t/n.width+n.x,this[i][4]=(this[i][4]-n.y)*e/n.height+n.y,"C"===r&&(this[i][5]=(this[i][5]-n.x)*t/n.width+n.x,this[i][6]=(this[i][6]-n.y)*e/n.height+n.y)):"A"===r&&(this[i][1]=this[i][1]*t/n.width,this[i][2]=this[i][2]*e/n.height,this[i][6]=(this[i][6]-n.x)*t/n.width+n.x,this[i][7]=(this[i][7]-n.y)*e/n.height+n.y);return this}toString(){return function(t){let e="";for(let n=0,i=t.length;n<i;n++)e+=t[n][0],null!=t[n][1]&&(e+=t[n][1],null!=t[n][2]&&(e+=" ",e+=t[n][2],null!=t[n][3]&&(e+=" ",e+=t[n][3],e+=" ",e+=t[n][4],null!=t[n][5]&&(e+=" ",e+=t[n][5],e+=" ",e+=t[n][6],null!=t[n][7]&&(e+=" ",e+=t[n][7])))));return e+" "}(this)}}const we=t=>{const e=typeof t;return"number"===e?jt:"string"===e?ht.isColor(t)?ht:et.test(t)?nt.test(t)?ye:Et:B.test(t)?jt:_e:Me.indexOf(t.constructor)>-1?t.constructor:Array.isArray(t)?Et:"object"===e?ve:_e};class ge{constructor(t){this._stepper=t||new ee("-"),this._from=null,this._to=null,this._type=null,this._context=null,this._morphObj=null}at(t){return this._morphObj.morph(this._from,this._to,t,this._stepper,this._context)}done(){return this._context.map(this._stepper.done).reduce((function(t,e){return t&&e}),!0)}from(t){return null==t?this._from:(this._from=this._set(t),this)}stepper(t){return null==t?this._stepper:(this._stepper=t,this)}to(t){return null==t?this._to:(this._to=this._set(t),this)}type(t){return null==t?this._type:(this._type=t,this)}_set(t){this._type||this.type(we(t));let e=new this._type(t);return this._type===ht&&(e=this._to?e[this._to[4]]():this._from?e[this._from[4]]():e),this._type===ve&&(e=this._to?e.align(this._to):this._from?e.align(this._from):e),e=e.toConsumable(),this._morphObj=this._morphObj||new this._type,this._context=this._context||Array.apply(null,Array(e.length)).map(Object).map((function(t){return t.done=!0,t})),e}}class _e{constructor(...t){this.init(...t)}init(t){return t=Array.isArray(t)?t[0]:t,this.value=t,this}toArray(){return[this.value]}valueOf(){return this.value}}class xe{constructor(...t){this.init(...t)}init(t){return Array.isArray(t)&&(t={scaleX:t[0],scaleY:t[1],shear:t[2],rotate:t[3],translateX:t[4],translateY:t[5],originX:t[6],originY:t[7]}),Object.assign(this,xe.defaults,t),this}toArray(){const t=this;return[t.scaleX,t.scaleY,t.shear,t.rotate,t.translateX,t.translateY,t.originX,t.originY]}}xe.defaults={scaleX:1,scaleY:1,shear:0,rotate:0,translateX:0,translateY:0,originX:0,originY:0};const be=(t,e)=>t[0]<e[0]?-1:t[0]>e[0]?1:0;class ve{constructor(...t){this.init(...t)}align(t){const e=this.values;for(let n=0,i=e.length;n<i;++n){if(e[n+1]===t[n+1]){if(e[n+1]===ht&&t[n+7]!==e[n+7]){const e=t[n+7],i=new ht(this.values.splice(n+3,5))[e]().toArray();this.values.splice(n+3,0,...i)}n+=e[n+2]+2;continue}if(!t[n+1])return this;const i=(new t[n+1]).toArray(),r=e[n+2]+3;e.splice(n,r,t[n],t[n+1],t[n+2],...i),n+=e[n+2]+2}return this}init(t){if(this.values=[],Array.isArray(t))return void(this.values=t.slice());t=t||{};const e=[];for(const n in t){const i=we(t[n]),r=new i(t[n]).toArray();e.push([n,i,r.length,...r])}return e.sort(be),this.values=e.reduce(((t,e)=>t.concat(e)),[]),this}toArray(){return this.values}valueOf(){const t={},e=this.values;for(;e.length;){const n=e.shift(),i=e.shift(),r=e.shift(),s=e.splice(0,r);t[n]=new i(s)}return t}}const Me=[_e,xe,ve];function Ae(t=[]){Me.push(...[].concat(t))}function Oe(){X(Me,{to(t){return(new ge).type(this.constructor).from(this.toArray()).to(t)},fromArray(t){return this.init(t),this},toConsumable(){return this.toArray()},morph(t,e,n,i,r){return this.fromArray(t.map((function(t,s){return i.step(t,e[s],n,r[s],r)})))}})}class Path extends Shape{constructor(t,e=t){super(D("path",t),e)}array(){return this._array||(this._array=new ye(this.attr("d")))}clear(){return delete this._array,this}height(t){return null==t?this.bbox().height:this.size(this.bbox().width,t)}move(t,e){return this.attr("d",this.array().move(t,e))}plot(t){return null==t?this.array():this.clear().attr("d","string"==typeof t?t:this._array=new ye(t))}size(t,e){const n=l(this,t,e);return this.attr("d",this.array().size(n.width,n.height))}width(t){return null==t?this.bbox().width:this.size(t,this.bbox().height)}x(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)}y(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)}}Path.prototype.MorphArray=ye,n({Container:{path:Y((function(t){return this.put(new Path).plot(t||new ye)}))}}),P(Path,"Path");var ke={__proto__:null,array:function(){return this._array||(this._array=new Qt(this.attr("points")))},clear:function(){return delete this._array,this},move:function(t,e){return this.attr("points",this.array().move(t,e))},plot:function(t){return null==t?this.array():this.clear().attr("points","string"==typeof t?t:this._array=new Qt(t))},size:function(t,e){const n=l(this,t,e);return this.attr("points",this.array().size(n.width,n.height))}};class Polygon extends Shape{constructor(t,e=t){super(D("polygon",t),e)}}n({Container:{polygon:Y((function(t){return this.put(new Polygon).plot(t||new Qt)}))}}),X(Polygon,Jt),X(Polygon,ke),P(Polygon,"Polygon");class Polyline extends Shape{constructor(t,e=t){super(D("polyline",t),e)}}n({Container:{polyline:Y((function(t){return this.put(new Polyline).plot(t||new Qt)}))}}),X(Polyline,Jt),X(Polyline,ke),P(Polyline,"Polyline");class Rect extends Shape{constructor(t,e=t){super(D("rect",t),e)}}X(Rect,{rx:Pt,ry:Rt}),n({Container:{rect:Y((function(t,e){return this.put(new Rect).size(t,e)}))}}),P(Rect,"Rect");class Te{constructor(){this._first=null,this._last=null}first(){return this._first&&this._first.value}last(){return this._last&&this._last.value}push(t){const e=void 0!==t.next?t:{value:t,next:null,prev:null};return this._last?(e.prev=this._last,this._last.next=e,this._last=e):(this._last=e,this._first=e),e}remove(t){t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t===this._last&&(this._last=t.prev),t===this._first&&(this._first=t.next),t.prev=null,t.next=null}shift(){const t=this._first;return t?(this._first=t.next,this._first&&(this._first.prev=null),this._last=this._first?this._last:null,t.value):null}}const Ce={nextDraw:null,frames:new Te,timeouts:new Te,immediates:new Te,timer:()=>b.window.performance||b.window.Date,transforms:[],frame(t){const e=Ce.frames.push({run:t});return null===Ce.nextDraw&&(Ce.nextDraw=b.window.requestAnimationFrame(Ce._draw)),e},timeout(t,e){e=e||0;const n=Ce.timer().now()+e,i=Ce.timeouts.push({run:t,time:n});return null===Ce.nextDraw&&(Ce.nextDraw=b.window.requestAnimationFrame(Ce._draw)),i},immediate(t){const e=Ce.immediates.push(t);return null===Ce.nextDraw&&(Ce.nextDraw=b.window.requestAnimationFrame(Ce._draw)),e},cancelFrame(t){null!=t&&Ce.frames.remove(t)},clearTimeout(t){null!=t&&Ce.timeouts.remove(t)},cancelImmediate(t){null!=t&&Ce.immediates.remove(t)},_draw(t){let e=null;const n=Ce.timeouts.last();for(;(e=Ce.timeouts.shift())&&(t>=e.time?e.run():Ce.timeouts.push(e),e!==n););let i=null;const r=Ce.frames.last();for(;i!==r&&(i=Ce.frames.shift());)i.run(t);let s=null;for(;s=Ce.immediates.shift();)s();Ce.nextDraw=Ce.timeouts.first()||Ce.frames.first()?b.window.requestAnimationFrame(Ce._draw):null}},Ne=function(t){const e=t.start,n=t.runner.duration();return{start:e,duration:n,end:e+n,runner:t.runner}},Se=function(){const t=b.window;return(t.performance||t.Date).now()};class Ee extends kt{constructor(t=Se){super(),this._timeSource=t,this.terminate()}active(){return!!this._nextFrame}finish(){return this.time(this.getEndTimeOfTimeline()+1),this.pause()}getEndTime(){const t=this.getLastRunnerInfo(),e=t?t.runner.duration():0;return(t?t.start:this._time)+e}getEndTimeOfTimeline(){const t=this._runners.map((t=>t.start+t.runner.duration()));return Math.max(0,...t)}getLastRunnerInfo(){return this.getRunnerInfoById(this._lastRunnerId)}getRunnerInfoById(t){return this._runners[this._runnerIds.indexOf(t)]||null}pause(){return this._paused=!0,this._continue()}persist(t){return null==t?this._persist:(this._persist=t,this)}play(){return this._paused=!1,this.updateTime()._continue()}reverse(t){const e=this.speed();if(null==t)return this.speed(-e);const n=Math.abs(e);return this.speed(t?-n:n)}schedule(t,e,n){if(null==t)return this._runners.map(Ne);let i=0;const r=this.getEndTime();if(e=e||0,null==n||"last"===n||"after"===n)i=r;else if("absolute"===n||"start"===n)i=e,e=0;else if("now"===n)i=this._time;else if("relative"===n){const n=this.getRunnerInfoById(t.id);n&&(i=n.start+e,e=0)}else{if("with-last"!==n)throw new Error('Invalid value for the "when" parameter');{const t=this.getLastRunnerInfo();i=t?t.start:this._time}}t.unschedule(),t.timeline(this);const s=t.persist(),o={persist:null===s?this._persist:s,start:i+e,runner:t};return this._lastRunnerId=t.id,this._runners.push(o),this._runners.sort(((t,e)=>t.start-e.start)),this._runnerIds=this._runners.map((t=>t.runner.id)),this.updateTime()._continue(),this}seek(t){return this.time(this._time+t)}source(t){return null==t?this._timeSource:(this._timeSource=t,this)}speed(t){return null==t?this._speed:(this._speed=t,this)}stop(){return this.time(0),this.pause()}time(t){return null==t?this._time:(this._time=t,this._continue(!0))}unschedule(t){const e=this._runnerIds.indexOf(t.id);return e<0||(this._runners.splice(e,1),this._runnerIds.splice(e,1),t.timeline(null)),this}updateTime(){return this.active()||(this._lastSourceTime=this._timeSource()),this}_continue(t=!1){return Ce.cancelFrame(this._nextFrame),this._nextFrame=null,t?this._stepImmediate():(this._paused||(this._nextFrame=Ce.frame(this._step)),this)}_stepFn(t=!1){const e=this._timeSource();let n=e-this._lastSourceTime;t&&(n=0);const i=this._speed*n+(this._time-this._lastStepTime);this._lastSourceTime=e,t||(this._time+=i,this._time=this._time<0?0:this._time),this._lastStepTime=this._time,this.fire("time",this._time);for(let t=this._runners.length;t--;){const e=this._runners[t],n=e.runner;this._time-e.start<=0&&n.reset()}let r=!1;for(let t=0,e=this._runners.length;t<e;t++){const n=this._runners[t],s=n.runner;let o=i;const h=this._time-n.start;if(h<=0){r=!0;continue}if(h<o&&(o=h),!s.active())continue;if(s.step(o).done){if(!0!==n.persist){s.duration()-s.time()+this._time+n.persist<this._time&&(s.unschedule(),--t,--e)}}else r=!0}return r&&!(this._speed<0&&0===this._time)||this._runnerIds.length&&this._speed<0&&this._time>0?this._continue():(this.pause(),this.fire("finished")),this}terminate(){this._startTime=0,this._speed=1,this._persist=0,this._nextFrame=null,this._paused=!0,this._runners=[],this._runnerIds=[],this._lastRunnerId=-1,this._time=0,this._lastSourceTime=0,this._lastStepTime=0,this._step=this._stepFn.bind(this,!1),this._stepImmediate=this._stepFn.bind(this,!0)}}n({Element:{timeline:function(t){return null==t?(this._timeline=this._timeline||new Ee,this._timeline):(this._timeline=t,this)}}});class je extends kt{constructor(t){super(),this.id=je.id++,t="function"==typeof(t=null==t?Ct.duration:t)?new ne(t):t,this._element=null,this._timeline=null,this.done=!1,this._queue=[],this._duration="number"==typeof t&&t,this._isDeclarative=t instanceof ne,this._stepper=this._isDeclarative?t:new ee,this._history={},this.enabled=!0,this._time=0,this._lastTime=0,this._reseted=!0,this.transforms=new lt,this.transformId=1,this._haveReversed=!1,this._reverse=!1,this._loopsDone=0,this._swing=!1,this._wait=0,this._times=1,this._frameId=null,this._persist=!!this._isDeclarative||null}static sanitise(t,e,n){let i=1,r=!1,s=0;return e=e??Ct.delay,n=n||"last","object"!=typeof(t=t??Ct.duration)||t instanceof te||(e=t.delay??e,n=t.when??n,r=t.swing||r,i=t.times??i,s=t.wait??s,t=t.duration??Ct.duration),{duration:t,delay:e,swing:r,times:i,wait:s,when:n}}active(t){return null==t?this.enabled:(this.enabled=t,this)}addTransform(t){return this.transforms.lmultiplyO(t),this}after(t){return this.on("finished",t)}animate(t,e,n){const i=je.sanitise(t,e,n),r=new je(i.duration);return this._timeline&&r.timeline(this._timeline),this._element&&r.element(this._element),r.loop(i).schedule(i.delay,i.when)}clearTransform(){return this.transforms=new lt,this}clearTransformsFromQueue(){this.done&&this._timeline&&this._timeline._runnerIds.includes(this.id)||(this._queue=this._queue.filter((t=>!t.isTransform)))}delay(t){return this.animate(0,t)}duration(){return this._times*(this._wait+this._duration)-this._wait}during(t){return this.queue(null,t)}ease(t){return this._stepper=new ee(t),this}element(t){return null==t?this._element:(this._element=t,t._prepareRunner(),this)}finish(){return this.step(1/0)}loop(t,e,n){return"object"==typeof t&&(e=t.swing,n=t.wait,t=t.times),this._times=t||1/0,this._swing=e||!1,this._wait=n||0,!0===this._times&&(this._times=1/0),this}loops(t){const e=this._duration+this._wait;if(null==t){const t=Math.floor(this._time/e),n=(this._time-t*e)/this._duration;return Math.min(t+n,this._times)}const n=t%1,i=e*Math.floor(t)+this._duration*n;return this.time(i)}persist(t){return null==t?this._persist:(this._persist=t,this)}position(t){const e=this._time,n=this._duration,i=this._wait,r=this._times,s=this._swing,o=this._reverse;let h;if(null==t){const t=function(t){const e=s*Math.floor(t%(2*(i+n))/(i+n)),r=e&&!o||!e&&o,h=Math.pow(-1,r)*(t%(i+n))/n+r;return Math.max(Math.min(h,1),0)},u=r*(i+n)-i;return h=e<=0?Math.round(t(1e-5)):e<u?t(e):Math.round(t(u-1e-5)),h}const u=Math.floor(this.loops()),a=s&&u%2==0;return h=u+(a&&!o||o&&a?t:1-t),this.loops(h)}progress(t){return null==t?Math.min(1,this._time/this.duration()):this.time(t*this.duration())}queue(t,e,n,i){this._queue.push({initialiser:t||Tt,runner:e||Tt,retarget:n,isTransform:i,initialised:!1,finished:!1});return this.timeline()&&this.timeline()._continue(),this}reset(){return this._reseted||(this.time(0),this._reseted=!0),this}reverse(t){return this._reverse=null==t?!this._reverse:t,this}schedule(t,e,n){if(t instanceof Ee||(n=e,e=t,t=this.timeline()),!t)throw Error("Runner cannot be scheduled without timeline");return t.schedule(this,e,n),this}step(t){if(!this.enabled)return this;t=null==t?16:t,this._time+=t;const e=this.position(),n=this._lastPosition!==e&&this._time>=0;this._lastPosition=e;const i=this.duration(),r=this._lastTime<=0&&this._time>0,s=this._lastTime<i&&this._time>=i;this._lastTime=this._time,r&&this.fire("start",this);const o=this._isDeclarative;this.done=!o&&!s&&this._time>=i,this._reseted=!1;let h=!1;return(n||o)&&(this._initialise(n),this.transforms=new lt,h=this._run(o?t:e),this.fire("step",this)),this.done=this.done||h&&o,s&&this.fire("finished",this),this}time(t){if(null==t)return this._time;const e=t-this._time;return this.step(e),this}timeline(t){return void 0===t?this._timeline:(this._timeline=t,this)}unschedule(){const t=this.timeline();return t&&t.unschedule(this),this}_initialise(t){if(t||this._isDeclarative)for(let e=0,n=this._queue.length;e<n;++e){const n=this._queue[e],i=this._isDeclarative||!n.initialised&&t;t=!n.finished,i&&t&&(n.initialiser.call(this),n.initialised=!0)}}_rememberMorpher(t,e){if(this._history[t]={morpher:e,caller:this._queue[this._queue.length-1]},this._isDeclarative){const t=this.timeline();t&&t.play()}}_run(t){let e=!0;for(let n=0,i=this._queue.length;n<i;++n){const i=this._queue[n],r=i.runner.call(this,t);i.finished=i.finished||!0===r,e=e&&i.finished}return e}_tryRetarget(t,e,n){if(this._history[t]){if(!this._history[t].caller.initialised){const e=this._queue.indexOf(this._history[t].caller);return this._queue.splice(e,1),!1}this._history[t].caller.retarget?this._history[t].caller.retarget.call(this,e,n):this._history[t].morpher.to(e),this._history[t].caller.finished=!1;const i=this.timeline();return i&&i.play(),!0}return!1}}je.id=0;class De{constructor(t=new lt,e=-1,n=!0){this.transforms=t,this.id=e,this.done=n}clearTransformsFromQueue(){}}X([je,De],{mergeWith(t){return new De(t.transforms.lmultiply(this.transforms),t.id)}});const Ie=(t,e)=>t.lmultiplyO(e),ze=t=>t.transforms;function Pe(){const t=this._transformationRunners.runners.map(ze).reduce(Ie,new lt);this.transform(t),this._transformationRunners.merge(),1===this._transformationRunners.length()&&(this._frameId=null)}class Re{constructor(){this.runners=[],this.ids=[]}add(t){if(this.runners.includes(t))return;const e=t.id+1;return this.runners.push(t),this.ids.push(e),this}clearBefore(t){const e=this.ids.indexOf(t+1)||1;return this.ids.splice(0,e,0),this.runners.splice(0,e,new De).forEach((t=>t.clearTransformsFromQueue())),this}edit(t,e){const n=this.ids.indexOf(t+1);return this.ids.splice(n,1,t+1),this.runners.splice(n,1,e),this}getByID(t){return this.runners[this.ids.indexOf(t+1)]}length(){return this.ids.length}merge(){let t=null;for(let e=0;e<this.runners.length;++e){const n=this.runners[e];if(t&&n.done&&t.done&&(!n._timeline||!n._timeline._runnerIds.includes(n.id))&&(!t._timeline||!t._timeline._runnerIds.includes(t.id))){this.remove(n.id);const i=n.mergeWith(t);this.edit(t.id,i),t=i,--e}else t=n}return this}remove(t){const e=this.ids.indexOf(t+1);return this.ids.splice(e,1),this.runners.splice(e,1),this}}n({Element:{animate(t,e,n){const i=je.sanitise(t,e,n),r=this.timeline();return new je(i.duration).loop(i).element(this).timeline(r.play()).schedule(i.delay,i.when)},delay(t,e){return this.animate(0,t,e)},_clearTransformRunnersBefore(t){this._transformationRunners.clearBefore(t.id)},_currentTransform(t){return this._transformationRunners.runners.filter((e=>e.id<=t.id)).map(ze).reduce(Ie,new lt)},_addRunner(t){this._transformationRunners.add(t),Ce.cancelImmediate(this._frameId),this._frameId=Ce.immediate(Pe.bind(this))},_prepareRunner(){null==this._frameId&&(this._transformationRunners=(new Re).add(new De(new lt(this))))}}});X(je,{attr(t,e){return this.styleAttr("attr",t,e)},css(t,e){return this.styleAttr("css",t,e)},styleAttr(t,e,n){if("string"==typeof e)return this.styleAttr(t,{[e]:n});let i=e;if(this._tryRetarget(t,i))return this;let r=new ge(this._stepper).to(i),s=Object.keys(i);return this.queue((function(){r=r.from(this.element()[t](s))}),(function(e){return this.element()[t](r.at(e).valueOf()),r.done()}),(function(e){const n=Object.keys(e),o=(h=s,n.filter((t=>!h.includes(t))));var h;if(o.length){const e=this.element()[t](o),n=new ve(r.from()).valueOf();Object.assign(n,e),r.from(n)}const u=new ve(r.to()).valueOf();Object.assign(u,e),r.to(u),s=n,i=e})),this._rememberMorpher(t,r),this},zoom(t,e){if(this._tryRetarget("zoom",t,e))return this;let n=new ge(this._stepper).to(new jt(t));return this.queue((function(){n=n.from(this.element().zoom())}),(function(t){return this.element().zoom(n.at(t),e),n.done()}),(function(t,i){e=i,n.to(t)})),this._rememberMorpher("zoom",n),this},transform(t,e,n){if(e=t.relative||e,this._isDeclarative&&!e&&this._tryRetarget("transform",t))return this;const i=lt.isMatrixLike(t);n=null!=t.affine?t.affine:null!=n?n:!i;const r=new ge(this._stepper).type(n?xe:lt);let s,o,h,u,a;return this.queue((function(){o=o||this.element(),s=s||c(t,o),a=new lt(e?void 0:o),o._addRunner(this),e||o._clearTransformRunnersBefore(this)}),(function(l){e||this.clearTransform();const{x:c,y:f}=new ut(s).transform(o._currentTransform(this));let d=new lt({...t,origin:[c,f]}),m=this._isDeclarative&&h?h:a;if(n){d=d.decompose(c,f),m=m.decompose(c,f);const t=d.rotate,e=m.rotate,n=[t-360,t,t+360],i=n.map((t=>Math.abs(t-e))),r=Math.min(...i),s=i.indexOf(r);d.rotate=n[s]}e&&(i||(d.rotate=t.rotate||0),this._isDeclarative&&u&&(m.rotate=u)),r.from(m),r.to(d);const p=r.at(l);return u=p.rotate,h=new lt(p),this.addTransform(h),o._addRunner(this),r.done()}),(function(e){(e.origin||"center").toString()!==(t.origin||"center").toString()&&(s=c(e,o)),t={...e,origin:s}}),!0),this._isDeclarative&&this._rememberMorpher("transform",r),this},x(t){return this._queueNumber("x",t)},y(t){return this._queueNumber("y",t)},ax(t){return this._queueNumber("ax",t)},ay(t){return this._queueNumber("ay",t)},dx(t=0){return this._queueNumberDelta("x",t)},dy(t=0){return this._queueNumberDelta("y",t)},dmove(t,e){return this.dx(t).dy(e)},_queueNumberDelta(t,e){if(e=new jt(e),this._tryRetarget(t,e))return this;const n=new ge(this._stepper).to(e);let i=null;return this.queue((function(){i=this.element()[t](),n.from(i),n.to(i+e)}),(function(e){return this.element()[t](n.at(e)),n.done()}),(function(t){n.to(i+new jt(t))})),this._rememberMorpher(t,n),this},_queueObject(t,e){if(this._tryRetarget(t,e))return this;const n=new ge(this._stepper).to(e);return this.queue((function(){n.from(this.element()[t]())}),(function(e){return this.element()[t](n.at(e)),n.done()})),this._rememberMorpher(t,n),this},_queueNumber(t,e){return this._queueObject(t,new jt(e))},cx(t){return this._queueNumber("cx",t)},cy(t){return this._queueNumber("cy",t)},move(t,e){return this.x(t).y(e)},amove(t,e){return this.ax(t).ay(e)},center(t,e){return this.cx(t).cy(e)},size(t,e){let n;return t&&e||(n=this._element.bbox()),t||(t=n.width/n.height*e),e||(e=n.height/n.width*t),this.width(t).height(e)},width(t){return this._queueNumber("width",t)},height(t){return this._queueNumber("height",t)},plot(t,e,n,i){if(4===arguments.length)return this.plot([t,e,n,i]);if(this._tryRetarget("plot",t))return this;const r=new ge(this._stepper).type(this._element.MorphArray).to(t);return this.queue((function(){r.from(this._element.array())}),(function(t){return this._element.plot(r.at(t)),r.done()})),this._rememberMorpher("plot",r),this},leading(t){return this._queueNumber("leading",t)},viewbox(t,e,n,i){return this._queueObject("viewbox",new dt(t,e,n,i))},update(t){return"object"!=typeof t?this.update({offset:arguments[0],color:arguments[1],opacity:arguments[2]}):(null!=t.opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",t.offset),this)}}),X(je,{rx:Pt,ry:Rt,from:Vt,to:$t}),P(je,"Runner");class Svg extends Container{constructor(t,e=t){super(D("svg",t),e),this.namespace()}defs(){return this.isRoot()?I(this.node.querySelector("defs"))||this.put(new Defs):this.root().defs()}isRoot(){return!this.node.parentNode||!(this.node.parentNode instanceof b.window.SVGElement)&&"#document-fragment"!==this.node.parentNode.nodeName}namespace(){return this.isRoot()?this.attr({xmlns:y,version:"1.1"}).attr("xmlns:xlink",_,g):this.root().namespace()}removeNamespace(){return this.attr({xmlns:null,version:null}).attr("xmlns:xlink",null,g).attr("xmlns:svgjs",null,g)}root(){return this.isRoot()?this:super.root()}}n({Container:{nested:Y((function(){return this.put(new Svg)}))}}),P(Svg,"Svg",!0);class Symbol extends Container{constructor(t,e=t){super(D("symbol",t),e)}}n({Container:{symbol:Y((function(){return this.put(new Symbol)}))}}),P(Symbol,"Symbol");var Le={__proto__:null,amove:function(t,e){return this.ax(t).ay(e)},ax:function(t){return this.attr("x",t)},ay:function(t){return this.attr("y",t)},build:function(t){return this._build=!!t,this},center:function(t,e,n=this.bbox()){return this.cx(t,n).cy(e,n)},cx:function(t,e=this.bbox()){return null==t?e.cx:this.attr("x",this.attr("x")+t-e.cx)},cy:function(t,e=this.bbox()){return null==t?e.cy:this.attr("y",this.attr("y")+t-e.cy)},length:function(){return this.node.getComputedTextLength()},move:function(t,e,n=this.bbox()){return this.x(t,n).y(e,n)},plain:function(t){return!1===this._build&&this.clear(),this.node.appendChild(b.document.createTextNode(t)),this},x:function(t,e=this.bbox()){return null==t?e.x:this.attr("x",this.attr("x")+t-e.x)},y:function(t,e=this.bbox()){return null==t?e.y:this.attr("y",this.attr("y")+t-e.y)}};class Text extends Shape{constructor(t,e=t){super(D("text",t),e),this.dom.leading=this.dom.leading??new jt(1.3),this._rebuild=!0,this._build=!1}leading(t){return null==t?this.dom.leading:(this.dom.leading=new jt(t),this.rebuild())}rebuild(t){if("boolean"==typeof t&&(this._rebuild=t),this._rebuild){const t=this;let e=0;const n=this.dom.leading;this.each((function(i){if(d(this.node))return;const r=b.window.getComputedStyle(this.node).getPropertyValue("font-size"),s=n*new jt(r);this.dom.newLined&&(this.attr("x",t.attr("x")),"\n"===this.text()?e+=s:(this.attr("dy",i?s+e:0),e=0))})),this.fire("rebuild")}return this}setData(t){return this.dom=t,this.dom.leading=new jt(t.leading||1.3),this}writeDataToDom(){return m(this,this.dom,{leading:1.3}),this}text(t){if(void 0===t){const e=this.node.childNodes;let n=0;t="";for(let i=0,r=e.length;i<r;++i)"textPath"===e[i].nodeName||d(e[i])?0===i&&(n=i+1):(i!==n&&3!==e[i].nodeType&&!0===I(e[i]).dom.newLined&&(t+="\n"),t+=e[i].textContent);return t}if(this.clear().build(!0),"function"==typeof t)t.call(this,this);else for(let e=0,n=(t=(t+"").split("\n")).length;e<n;e++)this.newLine(t[e]);return this.build(!1).rebuild()}}X(Text,Le),n({Container:{text:Y((function(t=""){return this.put(new Text).text(t)})),plain:Y((function(t=""){return this.put(new Text).plain(t)}))}}),P(Text,"Text");class Tspan extends Shape{constructor(t,e=t){super(D("tspan",t),e),this._build=!1}dx(t){return this.attr("dx",t)}dy(t){return this.attr("dy",t)}newLine(){this.dom.newLined=!0;const t=this.parent();if(!(t instanceof Text))return this;const e=t.index(this),n=b.window.getComputedStyle(this.node).getPropertyValue("font-size"),i=t.dom.leading*new jt(n);return this.dy(e?i:0).attr("x",t.x())}text(t){return null==t?this.node.textContent+(this.dom.newLined?"\n":""):("function"==typeof t?(this.clear().build(!0),t.call(this,this),this.build(!1)):this.plain(t),this)}}X(Tspan,Le),n({Tspan:{tspan:Y((function(t=""){const e=new Tspan;return this._build||this.clear(),this.put(e).text(t)}))},Text:{newLine:function(t=""){return this.tspan(t).newLine()}}}),P(Tspan,"Tspan");class Circle extends Shape{constructor(t,e=t){super(D("circle",t),e)}radius(t){return this.attr("r",t)}rx(t){return this.attr("r",t)}ry(t){return this.rx(t)}size(t){return this.radius(new jt(t).divide(2))}}X(Circle,{x:Lt,y:qt,cx:Ft,cy:Xt,width:Yt,height:Bt}),n({Container:{circle:Y((function(t=0){return this.put(new Circle).size(t).move(0,0)}))}}),P(Circle,"Circle");class ClipPath extends Container{constructor(t,e=t){super(D("clipPath",t),e)}remove(){return this.targets().forEach((function(t){t.unclip()})),super.remove()}targets(){return wt("svg [clip-path*="+this.id()+"]")}}n({Container:{clip:Y((function(){return this.defs().put(new ClipPath)}))},Element:{clipper(){return this.reference("clip-path")},clipWith(t){const e=t instanceof ClipPath?t:this.parent().clip().add(t);return this.attr("clip-path","url(#"+e.id()+")")},unclip(){return this.attr("clip-path",null)}}}),P(ClipPath,"ClipPath");class qe extends Element{constructor(t,e=t){super(D("foreignObject",t),e)}}n({Container:{foreignObject:Y((function(t,e){return this.put(new qe).size(t,e)}))}}),P(qe,"ForeignObject");var Fe={__proto__:null,dmove:function(t,e){return this.children().forEach((n=>{let i;try{i=n.node instanceof T().SVGSVGElement?new dt(n.attr(["x","y","width","height"])):n.bbox()}catch(t){return}const r=new lt(n),s=r.translate(t,e).transform(r.inverse()),o=new ut(i.x,i.y).transform(s);n.move(o.x,o.y)})),this},dx:function(t){return this.dmove(t,0)},dy:function(t){return this.dmove(0,t)},height:function(t,e=this.bbox()){return null==t?e.height:this.size(e.width,t,e)},move:function(t=0,e=0,n=this.bbox()){const i=t-n.x,r=e-n.y;return this.dmove(i,r)},size:function(t,e,n=this.bbox()){const i=l(this,t,e,n),r=i.width/n.width,s=i.height/n.height;return this.children().forEach((t=>{const e=new ut(n).transform(new lt(t).inverse());t.scale(r,s,e.x,e.y)})),this},width:function(t,e=this.bbox()){return null==t?e.width:this.size(t,e.height,e)},x:function(t,e=this.bbox()){return null==t?e.x:this.move(t,e.y,e)},y:function(t,e=this.bbox()){return null==t?e.y:this.move(e.x,t,e)}};class G extends Container{constructor(t,e=t){super(D("g",t),e)}}X(G,Fe),n({Container:{group:Y((function(){return this.put(new G)}))}}),P(G,"G");class A extends Container{constructor(t,e=t){super(D("a",t),e)}target(t){return this.attr("target",t)}to(t){return this.attr("href",t,_)}}X(A,Fe),n({Container:{link:Y((function(t){return this.put(new A).to(t)}))},Element:{unlink(){const t=this.linker();if(!t)return this;const e=t.parent();if(!e)return this.remove();const n=e.index(t);return e.add(this,n),t.remove(),this},linkTo(t){let e=this.linker();return e||(e=new A,this.wrap(e)),"function"==typeof t?t.call(e,e):e.to(t),this},linker(){const t=this.parent();return t&&"a"===t.node.nodeName.toLowerCase()?t:null}}}),P(A,"A");class Mask extends Container{constructor(t,e=t){super(D("mask",t),e)}remove(){return this.targets().forEach((function(t){t.unmask()})),super.remove()}targets(){return wt("svg [mask*="+this.id()+"]")}}n({Container:{mask:Y((function(){return this.defs().put(new Mask)}))},Element:{masker(){return this.reference("mask")},maskWith(t){const e=t instanceof Mask?t:this.parent().mask().add(t);return this.attr("mask","url(#"+e.id()+")")},unmask(){return this.attr("mask",null)}}}),P(Mask,"Mask");class Stop extends Element{constructor(t,e=t){super(D("stop",t),e)}update(t){return("number"==typeof t||t instanceof jt)&&(t={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),null!=t.opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",new jt(t.offset)),this}}n({Gradient:{stop:function(t,e,n){return this.put(new Stop).update(t,e,n)}}}),P(Stop,"Stop");class Style extends Element{constructor(t,e=t){super(D("style",t),e)}addText(t=""){return this.node.textContent+=t,this}font(t,e,n={}){return this.rule("@font-face",{fontFamily:t,src:e,...n})}rule(t,e){return this.addText(function(t,e){if(!t)return"";if(!e)return t;let n=t+"{";for(const t in e)n+=u(t)+":"+e[t]+";";return n+="}",n}(t,e))}}n("Dom",{style(t,e){return this.put(new Style).rule(t,e)},fontface(t,e,n){return this.put(new Style).font(t,e,n)}}),P(Style,"Style");class TextPath extends Text{constructor(t,e=t){super(D("textPath",t),e)}array(){const t=this.track();return t?t.array():null}plot(t){const e=this.track();let n=null;return e&&(n=e.plot(t)),null==t?n:this}track(){return this.reference("href")}}n({Container:{textPath:Y((function(t,e){return t instanceof Text||(t=this.text(t)),t.path(e)}))},Text:{path:Y((function(t,e=!0){const n=new TextPath;let i;if(t instanceof Path||(t=this.defs().path(t)),n.attr("href","#"+t,_),e)for(;i=this.node.firstChild;)n.node.appendChild(i);return this.put(n)})),textPath(){return this.findOne("textPath")}},Path:{text:Y((function(t){return t instanceof Text||(t=(new Text).addTo(this.parent()).text(t)),t.path(this)})),targets(){return wt("svg textPath").filter((t=>(t.attr("href")||"").includes(this.id())))}}}),TextPath.prototype.MorphArray=ye,P(TextPath,"TextPath");class Use extends Shape{constructor(t,e=t){super(D("use",t),e)}use(t,e){return this.attr("href",(e||"")+"#"+t,_)}}n({Container:{use:Y((function(t,e){return this.put(new Use).use(t,e)}))}}),P(Use,"Use");const Xe=j;X([Svg,Symbol,Image,Pattern,Marker],i("viewbox")),X([Line,Polyline,Polygon,Path],i("marker")),X(Text,i("Text")),X(Path,i("Path")),X(Defs,i("Defs")),X([Text,Tspan],i("Tspan")),X([Rect,Ellipse,Gradient,je],i("radius")),X(kt,i("EventTarget")),X(Dom,i("Dom")),X(Element,i("Element")),X(Shape,i("Shape")),X([Container,Ht],i("Container")),X(Gradient,i("Gradient")),X(je,i("Runner")),pt.extend([...new Set(e)]),Ae([jt,ht,dt,lt,Et,Qt,ye,ut]),Oe();var Ye={__proto__:null,A:A,Animator:Ce,Array:Et,Box:dt,Circle:Circle,ClipPath:ClipPath,Color:ht,Container:Container,Controller:ne,Defs:Defs,Dom:Dom,Ease:ee,Element:Element,Ellipse:Ellipse,EventTarget:kt,ForeignObject:qe,Fragment:Ht,G:G,Gradient:Gradient,Image:Image,Line:Line,List:pt,Marker:Marker,Mask:Mask,Matrix:lt,Morphable:ge,NonMorphable:_e,Number:jt,ObjectBag:ve,PID:se,Path:Path,PathArray:ye,Pattern:Pattern,Point:ut,PointArray:Qt,Polygon:Polygon,Polyline:Polyline,Queue:Te,Rect:Rect,Runner:je,SVG:Xe,Shape:Shape,Spring:re,Stop:Stop,Style:Style,Svg:Svg,Symbol:Symbol,Text:Text,TextPath:TextPath,Timeline:Ee,TransformBag:xe,Tspan:Tspan,Use:Use,adopt:I,assignNewId:F,clearEvents:vt,create:E,defaults:St,dispatch:Ot,easing:Kt,eid:q,extend:X,find:wt,getClass:R,getEventTarget:bt,getEvents:xt,getWindow:T,makeInstance:j,makeMorphable:Oe,mockAdopt:function(t=I){z=t},namespaces:x,nodeOrNew:D,off:At,on:Mt,parser:ct,regex:it,register:P,registerMorphableType:Ae,registerWindow:v,restoreWindow:k,root:S,saveWindow:O,utils:p,windowEvents:_t,withWindow:function(t,e){O(),v(t,t.document),e(t,t.document),k()},wrapWithAttrCheck:Y};function Be(t,e){return j(t,e)}return Object.assign(Be,Ye),Be}();
//# sourceMappingURL=svg.min.js.map
