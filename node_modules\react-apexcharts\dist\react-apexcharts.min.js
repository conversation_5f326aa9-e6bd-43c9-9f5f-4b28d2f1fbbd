"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=Charts;var _react=_interopRequireWildcard(require("react")),_apexcharts=_interopRequireDefault(require("apexcharts")),_propTypes=_interopRequireDefault(require("prop-types")),_excluded=["type","width","height","series","options"];function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){var t,r;return"function"!=typeof WeakMap?null:(t=new WeakMap,r=new WeakMap,(_getRequireWildcardCache=function(e){return e?r:t})(e))}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=_typeof(e)&&"function"!=typeof e)return{default:e};t=_getRequireWildcardCache(t);if(t&&t.has(e))return t.get(e);var r,n,o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(r in e)"default"!==r&&{}.hasOwnProperty.call(e,r)&&((n=i?Object.getOwnPropertyDescriptor(e,r):null)&&(n.get||n.set)?Object.defineProperty(o,r,n):o[r]=e[r]);return o.default=e,t&&t.set(e,o),o}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r,n=arguments[t];for(r in n)!{}.hasOwnProperty.call(n,r)||(e[r]=n[r])}return e}).apply(null,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var r,n=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var o=Object.getOwnPropertySymbols(e),i=0;i<o.length;i++)r=o[i],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r]);return n}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var r,n={};for(r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function omit(e,t){var r=_objectSpread({},e);return t.forEach(function(e){delete r[e]}),r}function deepEqual(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new WeakSet;if(e!==t){if("object"!==_typeof(e)||null===e||"object"!==_typeof(t)||null===t)return!1;if(!r.has(e)&&!r.has(t)){r.add(e),r.add(t);var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(var i=0,u=n;i<u.length;i++){var c=u[i];if(!o.includes(c)||!deepEqual(e[c],t[c],r))return!1}}}return!0}function Charts(e){function o(e){return e&&"object"===_typeof(e)&&!Array.isArray(e)}var t=e.type,r=void 0===t?"line":t,t=e.width,n=void 0===t?"100%":t,t=e.height,i=void 0===t?"auto":t,u=e.series,c=e.options,t=_objectWithoutProperties(e,_excluded),p=(0,_react.useRef)(null),a=(0,_react.useRef)(null),f=(0,_react.useRef)(),s=((0,_react.useEffect)(function(){f.current=c;var e=p.current;return a.current=new _apexcharts.default(e,s()),a.current.render(),function(){a.current&&"function"==typeof a.current.destroy&&a.current.destroy()}},[]),(0,_react.useEffect)(function(){var e=!deepEqual(a.current.w.config.series,u),t=!deepEqual(f.current,c)||i!==a.current.opts.chart.height||n!==a.current.opts.chart.width;(e||t)&&(!e||t?a.current.updateOptions(s()):a.current.updateSeries(u)),f.current=c},[c,u,i,n]),function(){return l(c,{chart:{type:r,height:i,width:n},series:u})}),l=function(t,r){var n=_objectSpread({},t);return o(t)&&o(r)&&Object.keys(r).forEach(function(e){o(r[e])&&e in t?n[e]=l(t[e],r[e]):Object.assign(n,_defineProperty({},e,r[e]))}),n},e=omit(t,Object.keys(Charts.propTypes));return _react.default.createElement("div",_extends({ref:p},e))}Charts.propTypes={type:_propTypes.default.string.isRequired,series:_propTypes.default.array.isRequired,options:_propTypes.default.object.isRequired,width:_propTypes.default.oneOfType([_propTypes.default.string,_propTypes.default.number]),height:_propTypes.default.oneOfType([_propTypes.default.string,_propTypes.default.number])};